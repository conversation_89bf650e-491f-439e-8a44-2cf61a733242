<?php

namespace Prime\Controller;

use Common\Controller\PrimeController;

class RecruitmentNoticeController extends PrimeController
{

    public function _initialize()
    {
        parent::_initialize();
    }

    /**
     * 招聘公告列表
     */
    public function index()
    {
        $where = [];
        
        // 搜索条件
        $keyword = I('get.keyword', '');
        if (!empty($keyword)) {
            $where['title|company_name'] = ['like', "%{$keyword}%"];
        }
        
        $status = I('get.status', '');
        if ($status !== '') {
            $where['status'] = intval($status);
        }

        $page = I('get.p', 1, 'intval');
        $pageSize = I('get.pageSize', 5, 'intval'); // 默认每页显示5条记录，支持用户选择

        // 限制分页数量范围，防止恶意请求
        $allowedPageSizes = [5, 10, 15, 20, 50];
        if (!in_array($pageSize, $allowedPageSizes)) {
            $pageSize = 5; // 默认值
        }

        $model = D('RecruitmentNotice');
        $result = $model->getNoticeList($where, $page, $pageSize);

        // 分页
        $totalPages = ceil($result['count'] / $pageSize);
        
        $this->assign('list', $result['list']);
        $this->assign('count', $result['count']);
        $this->assign('page', $page);
        $this->assign('totalPages', $totalPages);
        $this->assign('pageSize', $pageSize);
        $this->assign('allowedPageSizes', $allowedPageSizes);
        $this->assign('keyword', $keyword);
        $this->assign('status', $status);
        $this->assign('statusOptions', $model->status);
        
        $this->display();
    }

    /**
     * 添加/编辑招聘公告
     */
    public function edit()
    {
        $id = I('get.id', 0, 'intval');
        $model = D('RecruitmentNotice');

        if (IS_POST) {
            $data = I('post.');

            if ($model->create($data)) {
                if ($id) {
                    $data['id'] = $id;
                    $result = $model->save($data);
                } else {
                    $result = $model->add($data);
                }

                if ($result !== false) {
                    if (IS_AJAX) {
                        $this->ajaxReturn(['status' => 1, 'info' => '保存成功！', 'url' => U('index')]);
                    } else {
                        $this->success('保存成功！', U('index'));
                    }
                } else {
                    if (IS_AJAX) {
                        $this->ajaxReturn(['status' => 0, 'info' => '保存失败！']);
                    } else {
                        $this->error('保存失败！');
                    }
                }
            } else {
                if (IS_AJAX) {
                    $this->ajaxReturn(['status' => 0, 'info' => $model->getError()]);
                } else {
                    $this->error($model->getError());
                }
            }
        }

        $info = [];
        if ($id) {
            $info = $model->find($id);
            if (!$info) {
                $this->error('记录不存在！');
            }
        }

        $this->assign('info', $info);
        $this->assign('id', $id);
        $this->display();
    }

    /**
     * 删除招聘公告
     */
    public function delete()
    {
        $id = I('get.id', 0, 'intval');
        if (!$id) {
            $this->error('参数错误！');
        }

        $model = D('RecruitmentNotice');
        $result = $model->deleteNotice($id);

        if ($result) {
            $this->success('删除成功！');
        } else {
            $this->error('删除失败！');
        }
    }

    /**
     * 状态切换
     */
    public function toggle()
    {
        $id = I('get.id', 0, 'intval');
        if (!$id) {
            $this->error('参数错误！');
        }

        $model = D('RecruitmentNotice');
        $info = $model->find($id);
        if (!$info) {
            $this->error('记录不存在！');
        }

        $newStatus = $info['status'] ? 0 : 1;
        $result = $model->where(['id' => $id])->save(['status' => $newStatus]);

        if ($result !== false) {
            $this->success('状态更新成功！');
        } else {
            $this->error('状态更新失败！');
        }
    }

    /**
     * 岗位管理
     */
    public function posts()
    {
        $id = I('get.id', 0, 'intval');
        if (!$id) {
            $this->error('参数错误！');
        }

        $model = D('RecruitmentNotice');
        $notice = $model->find($id);
        if (!$notice) {
            $this->error('招聘公告不存在！');
        }

        if (IS_POST) {
            $postIds = I('post.post_ids', []);
            $result = $model->addNoticePosts($id, $postIds);

            if ($result !== false) {
                if (IS_AJAX) {
                    $this->ajaxReturn(['status' => 1, 'info' => '岗位关联成功！', 'url' => U('posts', ['id' => $id])]);
                } else {
                    $this->success('岗位关联成功！', U('posts', ['id' => $id]));
                }
            } else {
                if (IS_AJAX) {
                    $this->ajaxReturn(['status' => 0, 'info' => '岗位关联失败！']);
                } else {
                    $this->error('岗位关联失败！');
                }
            }
        }

        // 获取所有可用岗位（包含项目名称）
        $allPosts = M('ProjectPost')
            ->alias('pp')
            ->join('LEFT JOIN __PROJECT__ p ON pp.project_id = p.id')
            ->where(['pp.status' => 1])
            ->field('pp.*, p.name as project_name')
            ->select();
        
        // 获取已关联岗位
        $linkedPosts = $model->getNoticePosts($id);
        $linkedPostIds = array_column($linkedPosts, 'id');

        $this->assign('notice', $notice);
        $this->assign('allPosts', $allPosts);
        $this->assign('linkedPosts', $linkedPosts);
        $this->assign('linkedPostIds', $linkedPostIds);
        $this->assign('id', $id);
        $this->display();
    }

    /**
     * 岗位要求配置
     */
    public function requirements()
    {
        $noticeId = I('get.notice_id', 0, 'intval');
        $postId = I('get.post_id', 0, 'intval');
        
        if (!$noticeId || !$postId) {
            $this->error('参数错误！');
        }

        $requirementsModel = D('PostRequirements');

        if (IS_POST) {
            $data = I('post.');
            $data['notice_id'] = $noticeId;
            $data['post_id'] = $postId;

            $result = $requirementsModel->saveRequirements($data);

            if ($result !== false) {
                if (IS_AJAX) {
                    $this->ajaxReturn(['status' => 1, 'info' => '保存成功！', 'url' => U('posts', ['id' => $noticeId])]);
                } else {
                    $this->success('保存成功！', U('posts', ['id' => $noticeId]));
                }
            } else {
                if (IS_AJAX) {
                    $this->ajaxReturn(['status' => 0, 'info' => '保存失败！']);
                } else {
                    $this->error('保存失败！');
                }
            }
        }

        // 获取招聘公告信息
        $notice = D('RecruitmentNotice')->find($noticeId);
        
        // 获取岗位信息
        $post = M('ProjectPost')->find($postId);
        
        // 获取现有要求
        $requirements = $requirementsModel->getRequirements($postId, $noticeId);

        $this->assign('notice', $notice);
        $this->assign('post', $post);
        $this->assign('requirements', $requirements ?: []);
        $this->assign('genderOptions', $requirementsModel->genderOptions);
        $this->assign('educationOptions', $requirementsModel->educationOptions);
        $this->assign('afraidHeightsOptions', $requirementsModel->afraidHeightsOptions);
        $this->assign('maritalStatusOptions', $requirementsModel->maritalStatusOptions);
        $this->assign('freshGraduateOptions', $requirementsModel->freshGraduateOptions);
        $this->assign('politicalStatusOptions', $requirementsModel->politicalStatusOptions);
        $this->assign('noticeId', $noticeId);
        $this->assign('postId', $postId);
        $this->display();
    }

    /**
     * 批量配置岗位要求
     */
    public function batchRequirements()
    {
        $noticeId = I('get.notice_id', 0, 'intval');
        if (!$noticeId) {
            $this->error('参数错误！');
        }

        $requirementsModel = D('PostRequirements');

        if (IS_POST) {
            $postIds = I('post.post_ids', []);
            $requirements = I('post.');
            unset($requirements['post_ids']);

            if (empty($postIds)) {
                if (IS_AJAX) {
                    $this->ajaxReturn(['status' => 0, 'info' => '请选择要配置的岗位！']);
                } else {
                    $this->error('请选择要配置的岗位！');
                }
                return;
            }

            $result = $requirementsModel->batchSaveRequirements($noticeId, $postIds, $requirements);

            if ($result) {
                if (IS_AJAX) {
                    $this->ajaxReturn(['status' => 1, 'info' => '批量配置成功！', 'url' => U('posts', ['id' => $noticeId])]);
                } else {
                    $this->success('批量配置成功！', U('posts', ['id' => $noticeId]));
                }
            } else {
                if (IS_AJAX) {
                    $this->ajaxReturn(['status' => 0, 'info' => '批量配置失败！']);
                } else {
                    $this->error('批量配置失败！');
                }
            }
        }

        // 获取招聘公告信息
        $notice = D('RecruitmentNotice')->find($noticeId);
        
        // 获取公告下的岗位
        $posts = D('RecruitmentNotice')->getNoticePosts($noticeId);

        $this->assign('notice', $notice);
        $this->assign('posts', $posts);
        $this->assign('genderOptions', $requirementsModel->genderOptions);
        $this->assign('educationOptions', $requirementsModel->educationOptions);
        $this->assign('afraidHeightsOptions', $requirementsModel->afraidHeightsOptions);
        $this->assign('maritalStatusOptions', $requirementsModel->maritalStatusOptions);
        $this->assign('freshGraduateOptions', $requirementsModel->freshGraduateOptions);
        $this->assign('politicalStatusOptions', $requirementsModel->politicalStatusOptions);
        $this->assign('noticeId', $noticeId);
        $this->display();
    }

    /**
     * 执行简历匹配
     */
    public function executeMatch()
    {
        $noticeId = I('get.notice_id', 0, 'intval');
        $postId = I('get.post_id', 0, 'intval');
        if (!$noticeId) {
            $this->error('参数错误！');
        }

        $matchModel = D('ResumePostMatch');
        // 全量匹配：按公告下所有已关联岗位执行
        $result = $matchModel->executeMatching($noticeId);

        if ($result !== false) {
            $this->success("匹配完成！共处理 {$result} 条记录", U('matchResults', ['notice_id' => $noticeId]));
        } else {
            $this->error('匹配失败！');
        }
    }

    /**
     * 获取详细匹配信息（AJAX接口）
     */
    public function getMatchDetails()
    {
        $userJobId = I('get.user_job_id', 0, 'intval');
        $postId = I('get.post_id', 0, 'intval');
        $noticeId = I('get.notice_id', 0, 'intval');

        if (!$userJobId || !$postId || !$noticeId) {
            $this->ajaxReturn(['status' => 0, 'msg' => '参数错误']);
        }

        try {
            // 获取简历信息
            $userJob = D('UserJob')->find($userJobId);
            if (!$userJob) {
                $this->ajaxReturn(['status' => 0, 'msg' => '简历不存在']);
            }

            // 获取岗位要求
            $requirementsModel = D('PostRequirements');
            $requirements = $requirementsModel->getRequirements($postId, $noticeId);
            if (!$requirements) {
                $this->ajaxReturn(['status' => 0, 'msg' => '岗位要求不存在']);
            }

            // 获取岗位信息
            $post = M('ProjectPost')->find($postId);

            // 获取匹配结果
            $matchModel = D('ResumePostMatch');
            $matchResult = $matchModel->where([
                'user_job_id' => $userJobId,
                'post_id' => $postId,
                'notice_id' => $noticeId
            ])->find();

            // 重新计算匹配详情（确保数据最新）
            $matchDetails = $requirementsModel->checkResumeMatch($userJob, $requirements);

            // 计算应届生（与后端规则保持一致：毕业一年内且无工作经历）
            $isFreshGraduate = 0;
            $graduationDate = isset($userJob['graduation_date']) ? $userJob['graduation_date'] : '';
            $workYears = intval($userJob['work_experience_years']);
            if (!empty($graduationDate)) {
                $graduationTime = strtotime($graduationDate . '-01');
                $currentTime = time();
                $monthsDiff = (date('Y', $currentTime) - date('Y', $graduationTime)) * 12 + (date('m', $currentTime) - date('m', $graduationTime));
                $isFreshGraduate = ($monthsDiff <= 12 && $workYears == 0) ? 1 : 0;
            } else {
                $isFreshGraduate = ($workYears == 0) ? 1 : 0;
            }

            // 格式化数据
            $data = [
                'resume' => [
                    'name' => $userJob['name'],
                    'age' => $this->calculateAge($userJob['birthdate']),
                    'gender' => $userJob['gender'],
                    'height' => $userJob['height'],
                    'education_level' => $userJob['education_level'],
                    'major' => $userJob['major'],
                    'work_experience' => $userJob['work_experience_years'],
                    'political_status' => $userJob['political_status'],
                    'marital_status' => $userJob['marital_status'],
                    'is_afraid_heights' => $userJob['is_afraid_heights'],
                    'graduation_date' => $graduationDate,
                    'is_fresh_graduate' => $isFreshGraduate
                ],
                'requirements' => [
                    'min_age' => $requirements['min_age'],
                    'max_age' => $requirements['max_age'],
                    'gender' => $requirements['gender'],
                    'min_height' => $requirements['min_height'],
                    'max_height' => $requirements['max_height'],
                    'education_level' => $requirements['education_level'],
                    'major' => $requirements['major_keywords'],
                    'work_experience' => $requirements['min_work_years'],
                    'political_status' => $requirements['political_status'],
                    'marital_status' => $requirements['marital_status'],
                    'is_afraid_heights' => $requirements['is_afraid_heights'],
                    'graduation_years' => $requirements['graduation_years'],
                    'is_fresh_graduate' => $requirements['is_fresh_graduate']
                ],
                'post' => [
                    'job_name' => $post['job_name']
                ],
                'match_result' => [
                    'score' => $matchResult ? $matchResult['match_score'] : $matchDetails['score'],
                    'is_qualified' => $matchResult ? $matchResult['is_qualified'] : ($matchDetails['is_qualified'] ? 1 : 0),
                    'details' => $matchDetails['match_details'],
                    'passed_checks' => $matchDetails['passed_checks'],
                    'total_checks' => $matchDetails['total_checks']
                ]
            ];

            $this->ajaxReturn(['status' => 1, 'data' => $data]);

        } catch (Exception $e) {
            $this->ajaxReturn(['status' => 0, 'msg' => '获取数据失败：' . $e->getMessage()]);
        }
    }

    /**
     * 计算年龄
     */
    private function calculateAge($birthdate)
    {
        if (empty($birthdate)) {
            return 0;
        }

        $birth = new \DateTime($birthdate);
        $now = new \DateTime();
        return $birth->diff($now)->y;
    }

    /**
     * 匹配结果
     */
    public function matchResults()
    {
        $noticeId = I('get.notice_id', 0, 'intval');
        $postId = I('get.post_id', 0, 'intval');
        $isQualified = I('get.is_qualified', '');
        $minScore = I('get.min_score', 0, 'intval');
        $keyword = I('get.keyword', '', 'trim');
        $page = I('get.p', 1, 'intval');

        $where = [];
        if ($noticeId) {
            $where['notice_id'] = $noticeId;
        }
        if ($postId) {
            $where['post_id'] = $postId;
        }
        if ($isQualified !== '') {
            $where['is_qualified'] = intval($isQualified);
        }
        if ($minScore > 0) {
            $where['min_score'] = $minScore;
        }
        if (!empty($keyword)) {
            $where['keyword'] = $keyword;
        }

        $matchModel = D('ResumePostMatch');
        $result = $matchModel->getMatchResults($where, $page, 18);

        // 获取招聘公告列表
        $notices = D('RecruitmentNotice')->where(['status' => 1])->getField('id,title', true);
        
        // 获取岗位列表
        $posts = [];
        if ($noticeId) {
            $posts = D('RecruitmentNotice')->getNoticePosts($noticeId);
        }

        // 获取统计信息
        $stats = [];
        if ($noticeId) {
            $stats = $matchModel->getNoticeMatchStats($noticeId);
        }

        $pageSize = 18;
        $totalPages = ceil($result['count'] / $pageSize);

        // 计算显示记录范围
        $startRecord = ($page - 1) * $pageSize + 1;
        $endRecord = min($page * $pageSize, $result['count']);

        // 如果没有记录，调整显示
        if ($result['count'] == 0) {
            $startRecord = 0;
            $endRecord = 0;
        }

        $this->assign('list', $result['list']);
        $this->assign('count', $result['count']);
        $this->assign('page', $page);
        $this->assign('totalPages', $totalPages);
        $this->assign('pageSize', $pageSize);
        $this->assign('startRecord', $startRecord);
        $this->assign('endRecord', $endRecord);
        $this->assign('notices', $notices);
        $this->assign('posts', $posts);
        $this->assign('stats', $stats);
        $this->assign('noticeId', $noticeId);
        $this->assign('postId', $postId);
        $this->assign('isQualified', $isQualified);
        $this->assign('minScore', $minScore);
        $this->display();
    }
}
