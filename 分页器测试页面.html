<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分页器效果演示</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/3.4.1/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            padding: 20px;
            background-color: #f8f9fa;
        }
        
        .demo-container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .demo-title {
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #337ab7;
        }
        
        .demo-description {
            color: #666;
            margin-bottom: 20px;
            font-size: 14px;
        }

        /* 分页器样式优化 */
        .pagination {
            margin: 20px 0;
            justify-content: center;
        }

        .pagination > li > a,
        .pagination > li > span {
            padding: 8px 12px;
            margin: 0 2px;
            border: 1px solid #ddd;
            border-radius: 4px;
            color: #337ab7;
            text-decoration: none;
            background-color: #fff;
            transition: all 0.2s ease;
            font-size: 14px;
            min-width: 40px;
            text-align: center;
        }

        .pagination > li > a:hover,
        .pagination > li > a:focus {
            background-color: #e6f3ff;
            border-color: #337ab7;
            color: #23527c;
            text-decoration: none;
        }

        .pagination > .active > a,
        .pagination > .active > span,
        .pagination > .active > a:hover,
        .pagination > .active > span:hover,
        .pagination > .active > a:focus,
        .pagination > .active > span:focus {
            background-color: #337ab7;
            border-color: #337ab7;
            color: #fff;
            cursor: default;
            font-weight: bold;
        }

        .pagination > .disabled > span,
        .pagination > .disabled > span:hover,
        .pagination > .disabled > span:focus,
        .pagination > .disabled > a,
        .pagination > .disabled > a:hover,
        .pagination > .disabled > a:focus {
            color: #999;
            background-color: #f5f5f5;
            border-color: #ddd;
            cursor: not-allowed;
        }

        /* 首页末页按钮特殊样式 */
        .pagination > li:first-child > a,
        .pagination > li:first-child > span,
        .pagination > li:last-child > a,
        .pagination > li:last-child > span {
            font-weight: bold;
            background-color: #f8f9fa;
        }

        .pagination > li:first-child > a:hover,
        .pagination > li:last-child > a:hover {
            background-color: #e9ecef;
        }

        /* 省略号样式 */
        .pagination > .disabled > span {
            background-color: transparent;
            border: none;
            color: #999;
            cursor: default;
        }

        /* 分页信息样式 */
        .pagination-info {
            color: #666;
            font-size: 14px;
            margin-bottom: 10px;
            text-align: center;
        }

        /* 跳转输入框样式 */
        .pagination-jump {
            text-align: center;
            margin-top: 15px;
        }

        .pagination-jump .input-group {
            max-width: 200px;
            margin: 0 auto;
        }

        .pagination-jump .form-control {
            text-align: center;
            border-radius: 4px;
            height: 34px;
        }

        .pagination-jump .btn {
            border-radius: 4px;
            height: 34px;
        }

        .pagination-jump .input-group-addon {
            background-color: #f5f5f5;
            border-color: #ccc;
            color: #555;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center" style="color: #337ab7; margin-bottom: 40px;">分页器效果演示</h1>
        
        <!-- 场景1：少量页面 -->
        <div class="demo-container">
            <h3 class="demo-title">场景1：少量页面（5页）</h3>
            <p class="demo-description">当总页数较少时，显示所有页码</p>
            <div class="pagination-info">显示第 21-30 条，共 50 条记录</div>
            <div class="text-center">
                <ul class="pagination">
                    <li class="disabled"><span>&laquo;&laquo;</span></li>
                    <li class="disabled"><span>&laquo;</span></li>
                    <li><a href="#">1</a></li>
                    <li><a href="#">2</a></li>
                    <li class="active"><span>3</span></li>
                    <li><a href="#">4</a></li>
                    <li><a href="#">5</a></li>
                    <li><a href="#">&raquo;</a></li>
                    <li><a href="#">&raquo;&raquo;</a></li>
                </ul>
            </div>
        </div>

        <!-- 场景2：当前页在前部 -->
        <div class="demo-container">
            <h3 class="demo-title">场景2：当前页在前部</h3>
            <p class="demo-description">当前页在前部时，显示首页范围和末页</p>
            <div class="pagination-info">显示第 21-30 条，共 500 条记录</div>
            <div class="text-center">
                <ul class="pagination">
                    <li class="disabled"><span>&laquo;&laquo;</span></li>
                    <li class="disabled"><span>&laquo;</span></li>
                    <li><a href="#">1</a></li>
                    <li class="active"><span>2</span></li>
                    <li><a href="#">3</a></li>
                    <li><a href="#">4</a></li>
                    <li><a href="#">5</a></li>
                    <li class="disabled"><span>...</span></li>
                    <li><a href="#">50</a></li>
                    <li><a href="#">&raquo;</a></li>
                    <li><a href="#">&raquo;&raquo;</a></li>
                </ul>
            </div>
        </div>

        <!-- 场景3：当前页在中部 -->
        <div class="demo-container">
            <h3 class="demo-title">场景3：当前页在中部</h3>
            <p class="demo-description">当前页在中部时，显示首页、当前范围和末页</p>
            <div class="pagination-info">显示第 241-250 条，共 500 条记录</div>
            <div class="text-center">
                <ul class="pagination">
                    <li><a href="#">&laquo;&laquo;</a></li>
                    <li><a href="#">&laquo;</a></li>
                    <li><a href="#">1</a></li>
                    <li class="disabled"><span>...</span></li>
                    <li><a href="#">23</a></li>
                    <li><a href="#">24</a></li>
                    <li class="active"><span>25</span></li>
                    <li><a href="#">26</a></li>
                    <li><a href="#">27</a></li>
                    <li class="disabled"><span>...</span></li>
                    <li><a href="#">50</a></li>
                    <li><a href="#">&raquo;</a></li>
                    <li><a href="#">&raquo;&raquo;</a></li>
                </ul>
            </div>
        </div>

        <!-- 场景4：当前页在后部 -->
        <div class="demo-container">
            <h3 class="demo-title">场景4：当前页在后部</h3>
            <p class="demo-description">当前页在后部时，显示首页和末页范围</p>
            <div class="pagination-info">显示第 471-480 条，共 500 条记录</div>
            <div class="text-center">
                <ul class="pagination">
                    <li><a href="#">&laquo;&laquo;</a></li>
                    <li><a href="#">&laquo;</a></li>
                    <li><a href="#">1</a></li>
                    <li class="disabled"><span>...</span></li>
                    <li><a href="#">46</a></li>
                    <li><a href="#">47</a></li>
                    <li class="active"><span>48</span></li>
                    <li><a href="#">49</a></li>
                    <li><a href="#">50</a></li>
                    <li><a href="#">&raquo;</a></li>
                    <li class="disabled"><span>&raquo;&raquo;</span></li>
                </ul>
            </div>
        </div>

        <!-- 跳转功能演示 -->
        <div class="demo-container">
            <h3 class="demo-title">跳转功能</h3>
            <p class="demo-description">支持直接输入页码跳转到指定页面</p>
            <div class="pagination-jump">
                <div class="input-group">
                    <span class="input-group-addon">跳转到</span>
                    <input type="number" class="form-control text-center" min="1" max="50" value="25">
                    <span class="input-group-btn">
                        <button class="btn btn-primary" type="button">
                            <i class="glyphicon glyphicon-arrow-right"></i>
                        </button>
                    </span>
                </div>
            </div>
        </div>

        <!-- 功能说明 -->
        <div class="demo-container">
            <h3 class="demo-title">功能特点</h3>
            <div class="row">
                <div class="col-md-6">
                    <h4 style="color: #337ab7;">✨ 智能显示</h4>
                    <ul>
                        <li>自动计算最佳页码显示范围</li>
                        <li>智能省略号显示</li>
                        <li>始终显示首页和末页</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h4 style="color: #337ab7;">🎨 现代设计</h4>
                    <ul>
                        <li>圆角边框，现代化外观</li>
                        <li>平滑悬停动画效果</li>
                        <li>清晰的状态区分</li>
                    </ul>
                </div>
            </div>
            <div class="row" style="margin-top: 20px;">
                <div class="col-md-6">
                    <h4 style="color: #337ab7;">📱 响应式</h4>
                    <ul>
                        <li>移动端优化显示</li>
                        <li>触摸友好的按钮尺寸</li>
                        <li>自适应屏幕宽度</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h4 style="color: #337ab7;">⚡ 易用性</h4>
                    <ul>
                        <li>快速页码跳转功能</li>
                        <li>清晰的分页信息显示</li>
                        <li>直观的导航按钮</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/1.12.4/jquery.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/3.4.1/js/bootstrap.min.js"></script>
</body>
</html>
