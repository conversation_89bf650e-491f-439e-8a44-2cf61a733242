<include file="block/hat" />
<include file="RecruitmentNotice/common_styles" />

<div class="container-fluid recruitment-system">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title">
                        <span>
                            <i class="fa fa-cogs"></i> 批量配置岗位要求
                        </span>
                        <div class="pull-right">
                            <a href="{:U('posts', ['id' => $noticeId])}" class="btn btn-default btn-sm">
                                <i class="fa fa-arrow-left"></i> 返回岗位管理
                            </a>
                        </div>
                    </h3>
                </div>
                
                <div class="panel-body">
                    <div class="alert alert-info">
                        <strong>招聘公告：</strong>{$notice.title}
                        <br><strong>说明：</strong>以下配置将应用到所选的所有岗位
                    </div>
                    
                    <form class="form-horizontal js-ajax-form" method="post">
                        <!-- 岗位选择 -->
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <h4 class="panel-title">选择岗位</h4>
                            </div>
                            <div class="panel-body">
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="checkbox">
                                            <label>
                                                <input type="checkbox" id="selectAll"> 全选
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <volist name="posts" id="post">
                                        <div class="col-md-4 col-sm-6">
                                            <div class="checkbox">
                                                <label>
                                                    <input type="checkbox" name="post_ids[]" value="{$post.id}">
                                                    {$post.job_name}
                                                    <small class="text-muted">({$post.service_price})</small>
                                                </label>
                                            </div>
                                        </div>
                                    </volist>
                                </div>
                            </div>
                        </div>

                        <!-- 基本条件 -->
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <h4 class="panel-title">基本条件</h4>
                            </div>
                            <div class="panel-body">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">年龄要求</label>
                                    <div class="col-sm-8">
                                        <div class="row">
                                            <div class="col-sm-4">
                                                <input type="number" class="form-control" name="min_age" 
                                                       placeholder="最小年龄" min="16" max="65">
                                            </div>
                                            <div class="col-sm-1 text-center" style="padding-top: 7px;">至</div>
                                            <div class="col-sm-4">
                                                <input type="number" class="form-control" name="max_age" 
                                                       placeholder="最大年龄" min="16" max="65">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-sm-2 control-label">性别要求</label>
                                    <div class="col-sm-8">
                                        <volist name="genderOptions" id="option" key="k">
                                            <div class="radio-inline">
                                                <label>
                                                    <input type="radio" name="gender" value="{$k}" <if condition="$k eq 1">checked</if>>
                                                    {$option}
                                                </label>
                                            </div>
                                        </volist>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-sm-2 control-label">身高要求(cm)</label>
                                    <div class="col-sm-8">
                                        <div class="row">
                                            <div class="col-sm-4">
                                                <input type="number" class="form-control" name="min_height" 
                                                       placeholder="最小身高" min="140" max="220">
                                            </div>
                                            <div class="col-sm-1 text-center" style="padding-top: 7px;">至</div>
                                            <div class="col-sm-4">
                                                <input type="number" class="form-control" name="max_height" 
                                                       placeholder="最大身高" min="140" max="220">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-sm-2 control-label">婚姻状况</label>
                                    <div class="col-sm-8">
                                        <volist name="maritalStatusOptions" id="option" key="k">
                                            <div class="radio-inline">
                                                <label>
                                                    <input type="radio" name="marital_status" value="{$k}" <if condition="$k eq 0">checked</if>>
                                                    {$option}
                                                </label>
                                            </div>
                                        </volist>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-sm-2 control-label">恐高要求</label>
                                    <div class="col-sm-8">
                                        <volist name="afraidHeightsOptions" id="option" key="k">
                                            <div class="radio-inline">
                                                <label>
                                                    <input type="radio" name="is_afraid_heights" value="{$k}" <if condition="$k eq 0">checked</if>>
                                                    {$option}
                                                </label>
                                            </div>
                                        </volist>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 学历要求 -->
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <h4 class="panel-title">学历要求</h4>
                            </div>
                            <div class="panel-body">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">学历等级</label>
                                    <div class="col-sm-8">
                                        <volist name="educationOptions" id="option" key="k">
                                            <div class="radio-inline">
                                                <label>
                                                    <input type="radio" name="education_level" value="{$k}" <if condition="$k eq 0">checked</if>>
                                                    {$option}
                                                </label>
                                            </div>
                                        </volist>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-sm-2 control-label">专业关键词</label>
                                    <div class="col-sm-8">
                                        <input type="text" class="form-control" name="major_keywords" 
                                               placeholder="多个关键词用逗号分隔，如：计算机,软件工程,信息技术">
                                        <span class="help-block">支持模糊匹配，多个关键词用逗号分隔</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 毕业信息 -->
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <h4 class="panel-title">毕业信息</h4>
                            </div>
                            <div class="panel-body">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">毕业年限</label>
                                    <div class="col-sm-8">
                                        <input type="number" class="form-control" name="graduation_years"
                                               placeholder="如：3" min="0" max="20">
                                        <span class="help-block">设置求职者毕业年限要求，如输入3表示毕业3年内</span>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-sm-2 control-label">是否应届生</label>
                                    <div class="col-sm-8">
                                        <volist name="freshGraduateOptions" id="option" key="k">
                                            <div class="radio-inline">
                                                <label>
                                                    <input type="radio" name="is_fresh_graduate" value="{$k}"
                                                        <if condition="$k eq 0">checked</if>>
                                                    {$option}
                                                </label>
                                            </div>
                                        </volist>
                                        <span class="help-block">应届生判断标准：毕业一年内且无工作经历</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 其他要求 -->
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <h4 class="panel-title">其他要求</h4>
                            </div>
                            <div class="panel-body">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">政治面貌</label>
                                    <div class="col-sm-8">
                                        <input type="text" class="form-control" name="political_status"
                                               placeholder="如：党员，团员，群众等">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="col-sm-offset-2 col-sm-8">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fa fa-save"></i> 批量保存配置
                                </button>
                                <a href="{:U('posts', ['id' => $noticeId])}" class="btn btn-default">
                                    <i class="fa fa-times"></i> 取消
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="__ROOT__/static/js/prime/common.js"></script>
<script>
$(function() {
    // 全选/反选
    $('#selectAll').on('change', function() {
        var checked = $(this).prop('checked');
        $('input[name="post_ids[]"]').prop('checked', checked);
    });

    // Ajax表单提交
    $('.js-ajax-form').on('submit', function(e) {
        e.preventDefault();
        
        var $form = $(this);
        var $submitBtn = $form.find('button[type="submit"]');
        var originalText = $submitBtn.html();
        
        // 检查是否选择了岗位
        var checkedPosts = $form.find('input[name="post_ids[]"]:checked');
        if (checkedPosts.length === 0) {
            layer.msg('请至少选择一个岗位', {icon: 2});
            return;
        }
        
        // 禁用提交按钮
        $submitBtn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> 保存中...');
        
        $.ajax({
            url: $form.attr('action') || window.location.href,
            type: 'POST',
            data: $form.serialize(),
            dataType: 'json',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            success: function(response) {
                if (response.status == 1) {
                    alert(response.info);
                    if (response.url) {
                        window.location.href = response.url;
                    } else {
                        window.location.href = '{:U("posts", ["id" => $noticeId])}';
                    }
                } else {
                    alert(response.info);
                    $submitBtn.prop('disabled', false).html(originalText);
                }
            },
            error: function() {
                alert('网络错误，请重试');
                $submitBtn.prop('disabled', false).html(originalText);
            }
        });
    });
});
</script>
