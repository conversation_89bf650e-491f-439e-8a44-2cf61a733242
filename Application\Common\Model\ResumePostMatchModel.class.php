<?php
namespace Common\Model;

use Think\Model;

class ResumePostMatchModel extends Model
{
    protected $tableName = 'resume_post_match';
    
    protected $_auto = [
        ['create_time', 'time', 1, 'function'],
        ['update_time', 'time', 2, 'function'],
    ];

    /**
     * 执行简历匹配
     */
    public function executeMatching($noticeId, $postIds = [])
    {
        $postRequirementsModel = D('PostRequirements');
        $userJobModel = D('UserJob');

        // 如果没有指定岗位，获取公告下的所有岗位
        if (empty($postIds)) {
            $postIds = M('RecruitmentNoticePost')
                ->where(['notice_id' => $noticeId])
                ->getField('post_id', true);
        }

        if (empty($postIds)) {
            return false;
        }

        $matchCount = 0;

        foreach ($postIds as $postId) {
            // 获取岗位要求
            $requirements = $postRequirementsModel->getRequirements($postId, $noticeId);
            if (!$requirements) {
                continue;
            }

            // 获取所有简历
            $resumes = $userJobModel->select();
            
            foreach ($resumes as $resume) {
                // 检查匹配度
                $matchResult = $postRequirementsModel->checkResumeMatch($resume, $requirements);
                
                // 保存匹配结果
                $matchData = [
                    'user_job_id' => $resume['id'],
                    'post_id' => $postId,
                    'notice_id' => $noticeId,
                    'match_score' => $matchResult['score'],
                    'match_details' => json_encode($matchResult['match_details'], JSON_UNESCAPED_UNICODE),
                    'is_qualified' => $matchResult['is_qualified'] ? 1 : 0
                ];

                // 检查是否已存在记录
                $existing = $this->where([
                    'user_job_id' => $resume['id'],
                    'post_id' => $postId,
                    'notice_id' => $noticeId
                ])->find();

                if ($existing) {
                    $matchData['id'] = $existing['id'];
                    $this->save($matchData);
                } else {
                    $this->add($matchData);
                }

                $matchCount++;
            }
        }

        return $matchCount;
    }

    /**
     * 获取匹配结果列表
     */
    public function getMatchResults($where = [], $page = 1, $pageSize = 20, $order = 'match_score DESC')
    {
        // 构建WHERE条件
        $whereConditions = ['1=1'];

        if (!empty($where['notice_id'])) {
            $whereConditions[] = "m.notice_id = " . intval($where['notice_id']);
        }

        if (!empty($where['post_id'])) {
            $whereConditions[] = "m.post_id = " . intval($where['post_id']);
        }

        if (isset($where['is_qualified'])) {
            $whereConditions[] = "m.is_qualified = " . intval($where['is_qualified']);
        }

        if (!empty($where['min_score'])) {
            $whereConditions[] = "m.match_score >= " . floatval($where['min_score']);
        }
        // 关键字搜索：按简历姓名、手机号、专业模糊匹配
        if (!empty($where['keyword'])) {
            $kw = addslashes($where['keyword']);
            $whereConditions[] = "(u.name LIKE '%{$kw}%' OR u.phone LIKE '%{$kw}%' OR u.major LIKE '%{$kw}%' OR p.job_name LIKE '%{$kw}%')";
        }

        $whereClause = implode(' AND ', $whereConditions);

        // 计算总数
        $countSql = "SELECT COUNT(*) as count
                     FROM z_resume_post_match m
                     LEFT JOIN z_user_job u ON m.user_job_id = u.id
                     LEFT JOIN z_project_post p ON m.post_id = p.id
                     LEFT JOIN z_recruitment_notice n ON m.notice_id = n.id
                     WHERE {$whereClause}";

        $countResult = $this->query($countSql);
        $count = $countResult[0]['count'];

        // 获取列表数据
        $offset = ($page - 1) * $pageSize;
        $sql = "SELECT m.*, u.name, u.gender, u.phone, u.education_level, u.major,
                       p.job_name, n.title as notice_title
                FROM z_resume_post_match m
                LEFT JOIN z_user_job u ON m.user_job_id = u.id
                LEFT JOIN z_project_post p ON m.post_id = p.id
                LEFT JOIN z_recruitment_notice n ON m.notice_id = n.id
                WHERE {$whereClause}
                ORDER BY {$order}
                LIMIT {$offset}, {$pageSize}";

        $list = $this->query($sql);

        // 处理匹配详情
        foreach ($list as &$item) {
            if (!empty($item['match_details'])) {
                $item['match_details'] = json_decode($item['match_details'], true);
            }
        }

        return [
            'list' => $list,
            'count' => $count,
            'page' => $page,
            'pageSize' => $pageSize
        ];
    }

    /**
     * 获取招聘公告的匹配统计
     */
    public function getNoticeMatchStats($noticeId)
    {
        $noticeId = intval($noticeId);
        $sql = "SELECT
                    COUNT(*) as total_matches,
                    COUNT(CASE WHEN is_qualified = 1 THEN 1 END) as qualified_matches,
                    AVG(match_score) as avg_score,
                    MAX(match_score) as max_score,
                    MIN(match_score) as min_score
                FROM z_resume_post_match
                WHERE notice_id = {$noticeId}";

        $result = $this->query($sql);
        return $result[0];
    }

    /**
     * 获取岗位的匹配统计
     */
    public function getPostMatchStats($postId, $noticeId = null)
    {
        $postId = intval($postId);
        $sql = "SELECT
                    COUNT(*) as total_matches,
                    COUNT(CASE WHEN is_qualified = 1 THEN 1 END) as qualified_matches,
                    AVG(match_score) as avg_score
                FROM z_resume_post_match
                WHERE post_id = {$postId}";

        if ($noticeId) {
            $noticeId = intval($noticeId);
            $sql .= " AND notice_id = {$noticeId}";
        }

        $result = $this->query($sql);
        return $result[0];
    }

    /**
     * 删除匹配记录
     */
    public function deleteMatches($noticeId, $postIds = [])
    {
        $where = ['notice_id' => $noticeId];
        
        if (!empty($postIds)) {
            $where['post_id'] = ['IN', $postIds];
        }
        
        return $this->where($where)->delete();
    }
}
