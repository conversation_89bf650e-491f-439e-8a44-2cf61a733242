<include file="block/hat" />
<include file="RecruitmentNotice/common_styles" />

<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <div class="recruitment-page-wrapper">
                <div class="recruitment-page-container">
                    <!-- 现代化页面标题 -->
                    <div class="recruitment-page-header">
                        <div class="recruitment-header-content">
                            <div class="recruitment-page-title">
                                <div class="recruitment-title-icon">
                                    <i class="fa fa-bullhorn"></i>
                                </div>
                                <div class="recruitment-title-text">
                                    <h1 class="recruitment-title-main">
                                        <if condition="$id">编辑招聘公告<else />添加招聘公告</if>
                                    </h1>
                                    <p class="recruitment-title-sub">
                                        <if condition="$id">修改招聘公告信息<else />创建新的招聘公告</if>
                                    </p>
                                </div>
                            </div>
                            <div class="recruitment-header-actions">
                                <a href="{:U('index')}" class="recruitment-header-btn btn-secondary">
                                    <i class="fa fa-arrow-left"></i>
                                    <span>返回列表</span>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- 表单容器 -->
                    <div class="recruitment-form-container">
                        <form class="recruitment-form js-ajax-form" method="post" action="{:U('edit', ['id' => $id])}">
                            <div class="form-section">
                                <div class="form-section-title">
                                    <i class="fa fa-info-circle"></i>
                                    <span>基本信息</span>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">
                                            <i class="fa fa-file-text"></i>
                                            公告标题 <span class="required">*</span>
                                        </label>
                                        <input type="text" class="form-control" name="title" value="{$info.title}" placeholder="请输入公告标题" required>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">
                                            <i class="fa fa-building"></i>
                                            招聘单位 <span class="required">*</span>
                                        </label>
                                        <input type="text" class="form-control" name="company_name" value="{$info.company_name}" placeholder="请输入招聘单位名称" required>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">
                                            <i class="fa fa-align-left"></i>
                                            公告描述
                                        </label>
                                        <textarea class="form-control" name="description" rows="6" placeholder="请输入公告描述">{$info.description}</textarea>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">
                                            <i class="fa fa-toggle-on"></i>
                                            状态
                                        </label>
                                        <div class="radio-group">
                                            <label class="radio-option">
                                                <input type="radio" name="status" value="1" <if condition="empty($info.status) || $info.status eq 1">checked</if>>
                                                <span class="radio-custom"></span>
                                                <span class="radio-text">启用</span>
                                            </label>
                                            <label class="radio-option">
                                                <input type="radio" name="status" value="0" <if condition="!empty($info.status) && $info.status eq 0">checked</if>>
                                                <span class="radio-custom"></span>
                                                <span class="radio-text">停用</span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fa fa-save"></i>
                                    <span>保存</span>
                                </button>
                                <a href="{:U('index')}" class="btn btn-secondary btn-lg">
                                    <i class="fa fa-times"></i>
                                    <span>取消</span>
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="__ROOT__/static/js/prime/common.js"></script>
<script>
$(function() {
    // Ajax表单提交
    $('.js-ajax-form').on('submit', function(e) {
        e.preventDefault();
        
        var $form = $(this);
        var $submitBtn = $form.find('button[type="submit"]');
        var originalText = $submitBtn.html();
        
        // 禁用提交按钮
        $submitBtn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> 保存中...');
        
        $.ajax({
            url: $form.attr('action'),
            type: 'POST',
            data: $form.serialize(),
            dataType: 'json',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            success: function(response) {
                console.log('Response:', response); // 调试用
                if (response.status == 1) {
                    layer.msg(response.info, {icon: 1}, function() {
                        if (response.url) {
                            window.location.href = response.url;
                        } else {
                            window.location.href = '{:U("index")}';
                        }
                    });
                } else {
                    layer.msg(response.info || '操作失败', {icon: 2});
                    $submitBtn.prop('disabled', false).html(originalText);
                }
            },
            error: function(xhr, status, error) {
                console.log('Ajax Error:', xhr.responseText); // 调试用
                layer.msg('网络错误，请重试', {icon: 2});
                $submitBtn.prop('disabled', false).html(originalText);
            }
        });
    });
});
</script>

<style>
/* 表单容器样式 */
.recruitment-form-container {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    overflow: hidden;
}

.recruitment-form {
    padding: 2rem;
}

.form-section {
    margin-bottom: 2rem;
}

.form-section-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.6rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 1.5rem;
    padding-bottom: 0.75rem;
    border-bottom: 2px solid #e2e8f0;
}

.form-section-title i {
    color: #667eea;
    font-size: 1.45rem;
}

.form-row {
    margin-bottom: 1.5rem;
}

.form-group {
    width: 100%;
}

.form-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.35rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.75rem;
}

.form-label i {
    color: #667eea;
    width: 1.4rem;
}

.required {
    color: #ef4444;
    font-weight: 700;
}

.form-control {
    font-size: 1.35rem;
    padding: 1rem 1.25rem;
    border: 2px solid #e2e8f0;
    border-radius: 0.75rem;
    transition: all 0.3s ease;
    background: #f8fafc;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    background: white;
    outline: none;
}

.form-control::placeholder {
    color: #9ca3af;
    font-size: 1.2rem;
}

textarea.form-control {
    resize: vertical;
    min-height: 120px;
    line-height: 1.6;
}

/* 单选按钮组样式 */
.radio-group {
    display: flex;
    gap: 1.5rem;
    margin-top: 0.5rem;
}

.radio-option {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    font-size: 1.3rem;
    font-weight: 500;
    color: #374151;
    padding: 0.75rem 1.25rem;
    border: 2px solid #e2e8f0;
    border-radius: 0.75rem;
    transition: all 0.3s ease;
    background: #f8fafc;
}

.radio-option:hover {
    border-color: #667eea;
    background: white;
}

.radio-option input[type="radio"] {
    display: none;
}

.radio-custom {
    width: 1.25rem;
    height: 1.25rem;
    border: 2px solid #d1d5db;
    border-radius: 50%;
    position: relative;
    transition: all 0.3s ease;
}

.radio-option input[type="radio"]:checked + .radio-custom {
    border-color: #667eea;
    background: #667eea;
}

.radio-option input[type="radio"]:checked + .radio-custom::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 0.5rem;
    height: 0.5rem;
    background: white;
    border-radius: 50%;
}

.radio-option input[type="radio"]:checked ~ .radio-text {
    color: #667eea;
    font-weight: 600;
}

/* 表单操作按钮 */
.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    padding-top: 2rem;
    border-top: 2px solid #e2e8f0;
    margin-top: 2rem;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    border: none;
    border-radius: 0.75rem;
    font-size: 1.125rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    min-height: 3rem;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 6px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(102, 126, 234, 0.4);
    color: white;
    text-decoration: none;
}

.btn-secondary {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
    color: white;
    box-shadow: 0 4px 6px rgba(107, 114, 128, 0.3);
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #4b5563 0%, #374151 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(107, 114, 128, 0.4);
    color: white;
    text-decoration: none;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .recruitment-form {
        padding: 1.5rem;
    }

    .form-section-title {
        font-size: 1.25rem;
    }

    .form-label {
        font-size: 1rem;
    }

    .form-control {
        font-size: 1rem;
        padding: 0.875rem 1rem;
    }

    .radio-group {
        flex-direction: column;
        gap: 1rem;
    }

    .form-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .btn {
        justify-content: center;
        font-size: 1rem;
        padding: 0.875rem 1.5rem;
    }
}

@media (max-width: 480px) {
    .recruitment-form {
        padding: 1rem;
    }

    .form-section-title {
        font-size: 1.125rem;
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }

    .form-label {
        font-size: 0.875rem;
    }

    .form-control {
        font-size: 0.875rem;
        padding: 0.75rem;
    }
}
</style>
