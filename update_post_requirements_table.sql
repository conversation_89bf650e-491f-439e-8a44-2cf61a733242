-- 更新岗位要求表结构
-- 请在数据库中执行以下SQL语句

-- 1. 添加毕业年限字段
ALTER TABLE z_post_requirements ADD COLUMN graduation_years TINYINT(2) NULL COMMENT '毕业年限要求(年)' AFTER major_keywords;

-- 2. 添加是否应届生字段
ALTER TABLE z_post_requirements ADD COLUMN is_fresh_graduate TINYINT(1) DEFAULT 0 COMMENT '是否应届生: 0-不限, 1-仅限应届生, 2-非应届生' AFTER graduation_years;

-- 3. 可选：如果要完全移除工作经验字段（建议保留，用于应届生判断逻辑）
-- ALTER TABLE z_post_requirements DROP COLUMN min_work_years;
-- ALTER TABLE z_post_requirements DROP COLUMN max_work_years;

-- 4. 可选：如果要完全移除健康状况字段
-- ALTER TABLE z_post_requirements DROP COLUMN health_status;

-- 注意：
-- 1. 工作经验字段建议保留，用于应届生判断的逻辑计算
-- 2. 健康状况字段可以保留在数据库中，只是前端不显示
-- 3. 执行前请备份数据库
