<?php
namespace Common\Model;

use Think\Model;

class TrainingOrderModel extends Model
{
    protected $_auto = [
        ['create_time', 'time', self::MODEL_INSERT, 'function'],
        ['update_time', 'time', self::MODEL_BOTH, 'function'],
    ];

    // 保留原有状态定义，用于向后兼容
    public $order_status = [
        'new' => ['text' => '新建', 'style' => 'default'],
        'pending_payment' => ['text' => '待付款', 'style' => 'info'],
        'paid' => ['text' => '已付款', 'style' => 'primary'],
        'training' => ['text' => '培训中', 'style' => 'warning'],
        'completed' => ['text' => '已完成', 'style' => 'success'],
        'closed' => ['text' => '已关闭', 'style' => 'danger'],
    ];

    // 支付状态
    public $payment_status = [
        'unpaid' => ['text' => '未支付', 'style' => 'danger'],
        'paid' => ['text' => '已支付', 'style' => 'success'],
    ];

    // 奖励状态
    public $reward_status = [
        'locked' => ['text' => '锁定', 'style' => 'warning'],
        'unlocked' => ['text' => '已解锁', 'style' => 'success'],
    ];

    // 新增大状态定义
    public $main_status = [
        'communication' => ['text' => '沟通流程', 'style' => 'info', 'icon' => 'icon-chat'],
        'training' => ['text' => '培训流程', 'style' => 'warning', 'icon' => 'icon-book'],
        'employment' => ['text' => '入职流程', 'style' => 'primary', 'icon' => 'icon-work'],
        'service_review' => ['text' => '服务审查', 'style' => 'success', 'icon' => 'icon-check']
    ];

    // 新增小状态定义
    public $sub_status = [
        // 沟通流程
        'initial_contact' => [
            'text' => '初步沟通',
            'main' => 'communication',
            'desc' => '订单创建，开始与学员沟通',
            'next_actions' => ['intent_paid', 'contract_signed'],
            'allowed_operators' => ['admin'] // 只有管理员可以操作状态转换
        ],
        'intent_paid' => [
            'text' => '已付意向金',
            'main' => 'communication',
            'has_amount' => true,
            'amount_field' => 'intent_amount', // 用于前端显示，实际存储在payment_record表
            'desc' => '学员已支付意向金',
            'next_actions' => ['contract_signed'],
            'allowed_operators' => ['admin']
        ],
        'contract_signed' => [
            'text' => '已签合同待付款',
            'main' => 'communication',
            'desc' => '合同签署完成，等待付款',
            'next_actions' => ['partial_paid', 'full_paid'],
            'allowed_operators' => ['admin']
        ],
        'partial_paid' => [
            'text' => '部分付款',
            'main' => 'communication',
            'has_amount' => true,
            'amount_field' => 'partial_amount', // 用于前端显示，实际存储在payment_record表
            'desc' => '学员已部分付款',
            'next_actions' => ['full_paid'],
            'allowed_operators' => ['admin']
        ],
        'full_paid' => [
            'text' => '已全额付款',
            'main' => 'communication',
            'has_amount' => true,
            'amount_field' => 'fee_amount',
            'desc' => '学员已全额付款，可进入培训流程',
            'next_actions' => ['pending_notice'],
            'trigger_freeze' => true, // 触发资金冻结
            'allowed_operators' => ['admin']
        ],

        // 培训流程
        'pending_notice' => [
            'text' => '待通知培训',
            'main' => 'training',
            'desc' => '等待培训机构通知',
            'next_actions' => ['notice_sent'],
            'allowed_operators' => ['admin']
        ],
        'notice_sent' => [
            'text' => '已通知培训',
            'main' => 'training',
            'desc' => '已通知学员培训安排',
            'next_actions' => ['docs_signed'],
            'allowed_operators' => ['admin']
        ],
        'docs_signed' => [
            'text' => '已签署文件',
            'main' => 'training',
            'desc' => '学员已签署培训相关文件',
            'next_actions' => ['submitted_institute'],
            'allowed_operators' => ['admin']
        ],
        'submitted_institute' => [
            'text' => '已提交至培训机构',
            'main' => 'training',
            'desc' => '资料已提交给培训机构',
            'next_actions' => ['in_training'],
            'allowed_operators' => ['admin']
        ],
        'in_training' => [
            'text' => '参与培训中',
            'main' => 'training',
            'desc' => '学员正在参与培训',
            'next_actions' => ['end_training'],
            'allowed_operators' => ['admin']
        ],
        'end_training' => [
            'text' => '培训结束',
            'main' => 'training',
            'desc' => '学员培训结束',
            'next_actions' => ['waiting_examination'],
            'allowed_operators' => ['admin']
        ],

        // 入职流程：待通知笔试、已通知笔试、已参加笔试、待通知面试、已通知面试、已参加面试
        'waiting_examination' => [
            'text' => '待通知笔试',
            'main' => 'employment',
            'desc' => '等待企业笔试通知',
            'next_actions' => ['note_examination'],
            'allowed_operators' => ['admin']
        ],
        'note_examination' => [
            'text' => '已通知笔试',
            'main' => 'employment',
            'desc' => '企业已通知笔试',
            'next_actions' => ['in_examinationloyed'],
            'allowed_operators' => ['admin']
        ],
        'in_examinationloyed' => [
            'text' => '已参加笔试',
            'main' => 'employment',
            'desc' => '已参加企业笔试',
            'next_actions' => ['waiting_interview'],
            'allowed_operators' => ['admin']
        ],
        'waiting_interview' => [
            'text' => '待通知面试',
            'main' => 'employment',
            'desc' => '待企业通知面试',
            'next_actions' => ['note_interview'],
            'allowed_operators' => ['admin']
        ],
        'note_interview' => [
            'text' => '已通知面试',
            'main' => 'employment',
            'desc' => '企业已通知面试',
            'next_actions' => ['int_interviewyed'],
            'allowed_operators' => ['admin']
        ],
        'int_interviewyed' => [
            'text' => '已参加面试',
            'main' => 'employment',
            'desc' => '已参加企业面试',
            'next_actions' => ['waiting_notice'],
            'allowed_operators' => ['admin']
        ],
        'waiting_notice' => [
            'text' => '等待入职通知',
            'main' => 'employment',
            'desc' => '等待企业入职通知',
            'next_actions' => ['employed'],
            'allowed_operators' => ['admin']
        ],
        'employed' => [
            'text' => '已入职',
            'main' => 'employment',
            'desc' => '学员成功入职',
            'next_actions' => ['confirming'],
            'auto_next' => true, // 自动进入下一状态
            'allowed_operators' => ['admin']
        ],

        // 服务审查
        'confirming' => [
            'text' => '服务完成确认中',
            'main' => 'service_review',
            'desc' => '确认服务是否完成',
            'next_actions' => ['completed', 'terminated'],
            'allowed_operators' => ['admin']
        ],
        'terminated' => [
            'text' => '服务终止',
            'main' => 'service_review',
            'desc' => '服务提前终止',
            'is_final' => true,
            'trigger_refund' => true, // 触发退款处理
            'allowed_operators' => ['admin']
        ],
        'completed' => [
            'text' => '服务完成',
            'main' => 'service_review',
            'desc' => '服务完成，解冻分发资金',
            'is_final' => true,
            'trigger_unfreeze' => true, // 触发资金解冻分发
            'allowed_operators' => ['admin']
        ]
    ];

    /**
     * 获取培训订单列表
     * @param array $where 查询条件
     * @param string $order 排序
     * @param int $firstRow 起始行
     * @param int $listRows 每页行数
     * @return array 订单列表
     */
    public function getOrderList($where = [], $order = 'create_time desc', $firstRow = 0, $listRows = 20)
    {
        $list = $this->where($where)
            ->order($order)
            ->limit($firstRow, $listRows)
            ->select();

        if (!$list) {
            return [];
        }

        // 获取关联数据
        $userIds = array_column($list, 'user_id');
        $postIds = array_column($list, 'post_id');
        $stationIds = array_column($list, 'station_id');
        $parentStationIds = array_column($list, 'parent_station_id');
        $userJobIds = array_column($list, 'user_job_id');
        $zsbIds = array_column($list, 'zsb_id'); // 获取招就办ID

        // 合并所有服务站ID（包括招就办ID）
        $allStationIds = array_unique(array_merge($stationIds, array_filter($parentStationIds), array_filter($zsbIds)));

        // 获取用户信息
        $userList = D('User')->where(['id' => ['in', $userIds]])->getField('id,nickname as realname,mobile', true);

        // 获取简历信息 - 如果订单中有关联简历ID
        $userJobList = [];
        if (!empty($userJobIds)) {
            $userJobList = D('UserJob')->where(['id' => ['in', $userJobIds]])->getField('id,name,phone', true);
        }

        // 获取岗位信息
        $postList = D('ProjectPost')->where(['id' => ['in', $postIds]])->select();
        $postMap = [];
        $projectIds = [];
        foreach ($postList as $post) {
            $postMap[$post['id']] = $post;
            $projectIds[] = $post['project_id'];
        }

        // 获取项目信息
        $projectList = D('Project')->where(['id' => ['in', $projectIds]])->getField('id,name', true);

        // 获取服务站信息（包含联系人姓名）
        $stationList = D('ServiceStation')->where(['id' => ['in', $allStationIds]])->getField('id,service_name,contract_name', true);

        // 组装数据
        foreach ($list as &$item) {
            // 优先使用简历信息，如果有的话
            if (!empty($item['user_job_id']) && isset($userJobList[$item['user_job_id']])) {
                $item['user_name'] = $userJobList[$item['user_job_id']]['name'];
                $item['user_mobile'] = $userJobList[$item['user_job_id']]['phone'];
            } else {
                $item['user_name'] = isset($userList[$item['user_id']]) ? $userList[$item['user_id']]['realname'] : '';
                $item['user_mobile'] = isset($userList[$item['user_id']]) ? $userList[$item['user_id']]['mobile'] : '';
            }

            $item['post_name'] = isset($postMap[$item['post_id']]) ? $postMap[$item['post_id']]['job_name'] : '';
            $item['project_name'] = isset($postMap[$item['post_id']]) && isset($projectList[$postMap[$item['post_id']]['project_id']]) ?
                $projectList[$postMap[$item['post_id']]['project_id']] : '';
            $item['station_name'] = isset($stationList[$item['station_id']]) ? $stationList[$item['station_id']]['service_name'] : '';
            $item['parent_station_name'] = isset($stationList[$item['parent_station_id']]) ? $stationList[$item['parent_station_id']]['service_name'] : '';
            // 新的二级状态显示
            $item['main_status_text'] = isset($this->main_status[$item['main_status']]) ? $this->main_status[$item['main_status']]['text'] : '';
            $item['main_status_style'] = isset($this->main_status[$item['main_status']]) ? $this->main_status[$item['main_status']]['style'] : 'default';
            $item['main_status_icon'] = isset($this->main_status[$item['main_status']]) ? $this->main_status[$item['main_status']]['icon'] : '';

            $item['sub_status_text'] = isset($this->sub_status[$item['sub_status']]) ? $this->sub_status[$item['sub_status']]['text'] : '';
            $item['sub_status_desc'] = isset($this->sub_status[$item['sub_status']]) ? $this->sub_status[$item['sub_status']]['desc'] : '';

            // 添加奖励状态文本和样式
            $item['reward_status_text'] = isset($this->reward_status[$item['reward_status']]) ? $this->reward_status[$item['reward_status']]['text'] : '';
            $item['reward_status_style'] = isset($this->reward_status[$item['reward_status']]) ? $this->reward_status[$item['reward_status']]['style'] : 'warning';

            // 旧状态显示已完全移除，只使用新的二级状态系统

            // 获取分阶段付款金额
            $paymentAmounts = $this->getOrderPaymentAmounts($item['id']);
            $item = array_merge($item, $paymentAmounts);

            // 招就办订单相关信息
            $item['is_zsb_order'] = !empty($item['zsb_id']) && $item['zsb_id'] > 0;
            if ($item['is_zsb_order']) {
                // 招就办订单
                $item['order_type'] = 'zsb';
                $item['order_type_text'] = '招就办订单';
                $item['order_type_style'] = 'success';
                $item['zsb_name'] = isset($stationList[$item['zsb_id']]) ? $stationList[$item['zsb_id']]['contract_name'] : '';

                // 价格信息（转换为元显示）
                $item['zsb_price_yuan'] = !empty($item['zsb_price']) ? round($item['zsb_price'] / 100, 2) : 0;
                $item['zsb_cost_price_yuan'] = !empty($item['zsb_cost_price']) ? round($item['zsb_cost_price'] / 100, 2) : 0;
                $item['platform_fee_yuan'] = !empty($item['platform_fee']) ? round($item['platform_fee'] / 100, 2) : 0;
                $item['station_profit_yuan'] = !empty($item['station_profit']) ? round($item['station_profit'] / 100, 2) : 0;
                $item['zsb_commission_yuan'] = !empty($item['zsb_commission']) ? round($item['zsb_commission'] / 100, 2) : 0;
            } else {
                // 服务站自有订单
                $item['order_type'] = 'station';
                $item['order_type_text'] = '自有订单';
                $item['order_type_style'] = 'primary';
                $item['zsb_name'] = '';

                // 清空招就办相关字段
                $item['zsb_price_yuan'] = 0;
                $item['zsb_cost_price_yuan'] = 0;
                $item['platform_fee_yuan'] = 0;
                $item['station_profit_yuan'] = 0;
                $item['zsb_commission_yuan'] = 0;
            }
        }

        return $list;
    }

    /**
     * 创建培训订单
     * @param array $data 订单数据
     * @return int|bool 成功返回订单ID，失败返回false
     */
    public function createOrder($data)
    {
        // 获取服务站上级ID
        if (!isset($data['parent_station_id']) || !$data['parent_station_id']) {
            $stationInfo = D('ServiceStation')->where(['id' => $data['station_id']])->find();
            $data['parent_station_id'] = $stationInfo ? $stationInfo['pid'] : 0;
        }

        // 设置初始状态（新的二级状态系统）
        $data['main_status'] = 'communication';
        $data['sub_status'] = 'initial_contact';
        $data['status_desc'] = '初步沟通';
        $data['status_update_time'] = time();
        $data['status_operator_id'] = session('admin_id') ?: 0;
        $data['status_remark'] = '订单创建';

        // 旧状态字段已移除，完全使用新的二级状态系统

        // 生成自定义ID
        $data['id'] = $this->generateCustomId($data);

        // 计算服务站奖励
        $this->calculateOrderReward($data);

        // 计算平台收入
        $this->calculatePlatformCommission($data);

        // 创建订单
        if ($this->create($data)) {
            return $this->add();
        }

        return false;
    }

    /**
     * 生成自定义ID
     * 由于数据库id字段设置了AUTO_INCREMENT，使用时间戳+随机数策略
     * 确保ID唯一性和避免2147483647问题
     * @param array $data 订单数据
     * @return int 自定义ID
     */
    private function generateCustomId($data)
    {
        // 确定订单类型（用于日志记录）
        $orderType = (!empty($data['zsb_id']) && $data['zsb_id'] > 0) ? 2 : 1; // 1=自有订单，2=招就办订单
        $stationId = $orderType == 2 ? $data['zsb_id'] : $data['station_id'];

        // 生成基于时间戳的安全ID
        $maxAttempts = 50;
        $attempts = 0;

        do {
            $attempts++;

            // 生成时间戳基础ID，添加微秒和随机数确保唯一性
            $microtime = microtime(true);
            $timestamp = intval($microtime);
            $microseconds = intval(($microtime - $timestamp) * 1000000); // 获取微秒部分

            // 组合ID：时间戳 + 微秒后3位 + 随机数
            $customId = $timestamp * 1000 + ($microseconds % 1000) + rand(1, 999);

            // 确保不会生成问题ID值2147483647
            if ($customId >= 2147483647) {
                // 使用更小的时间戳基础
                $customId = $timestamp + rand(1000, 99999);
                if ($customId >= 2147483647) {
                    $customId = rand(1000000000, 1800000000); // 使用安全的随机范围
                }
            }

            // 检查数据库中是否已存在
            $existingOrder = $this->where(['id' => $customId])->find();

        } while ($existingOrder && $attempts < $maxAttempts);

        // 如果尝试次数过多，使用最终的安全策略
        if ($attempts >= $maxAttempts) {
            $customId = rand(1000000000, 1800000000);
            \Think\Log::write('ID生成尝试次数过多，使用随机数方案: ' . $customId, 'WARN');
        }

        // 最终安全检查
        if ($customId >= 2147483647) {
            $customId = rand(1000000000, 1800000000);
            \Think\Log::write('最终安全检查，重新生成ID: ' . $customId, 'ERROR');
        }

        \Think\Log::write('生成订单ID: ' . $customId . ' (订单类型:' . ($orderType == 2 ? '招就办' : '自有') . ', 服务站ID:' . $stationId . ', 尝试次数:' . $attempts . ')', 'INFO');

        return $customId;
    }

    /**
     * 计算订单的服务站奖励
     * @param array &$data 订单数据（引用传递，直接修改原数组）
     * @return void
     */
    private function calculateOrderReward(&$data)
    {
        // 获取岗位信息
        $post = D('ProjectPost')->where(['id' => $data['post_id']])->find();
        if (!$post) return;

        // 记录岗位信息
        \Think\Log::write('奖励计算 - 岗位信息: post_id=' . $data['post_id'] .
            ', service_price=' . $post['service_price'] .
            ', max_price=' . $post['max_price'] .
            ', reward=' . $post['reward'] .
            ', is_free=' . $post['is_free'], 'INFO');

        // 检查是否为公益项目
        if ($post['is_free'] == 1) {
            \Think\Log::write('奖励计算 - 公益项目，报名费和奖励均设为0', 'INFO');
            $data['fee_amount'] = 0; // 分单位
            $data['reward_station_amt'] = 0; // 分单位
            $data['reward_parent_amt'] = 0; // 分单位
            return;
        }

        // 获取项目身份成本
        $projectIdentityCost = 0;
        $projectJoinIdentity = D("ProjectJoinIdentity")->where([
            'project_id' => $post['project_id'],
            'project_post_id' => $data['post_id'],
            'project_identity_id' => 3,
        ])->find();

        if ($projectJoinIdentity) {
            $projectIdentityCost = $projectJoinIdentity['cost'];
            \Think\Log::write('奖励计算 - 项目身份成本: projectJoinIdentity_id=' . $projectJoinIdentity['id'] .
                ', cost=' . $projectIdentityCost, 'INFO');
        } else {
            \Think\Log::write('奖励计算 - 未找到项目身份成本记录，使用默认值0', 'INFO');
        }

        // 计算服务站奖励金额 - 使用新的计算方式
        $servicePrice = $post['service_price']; // 服务价格（元单位）
        $maxPrice = $post['max_price']; // 最高价格（元单位）
        $feeAmount = $data['fee_amount']; // 用户输入的报名费（分单位）
        $feeAmountYuan = round($feeAmount / 100, 2); // 转换为元单位用于计算

        \Think\Log::write('奖励计算 - 价格信息: servicePrice=' . $servicePrice .
            ', maxPrice=' . $maxPrice .
            ', feeAmount=' . $feeAmount . '分(' . $feeAmountYuan . '元)', 'INFO');

        // 获取平台费率
        $platformRate = $this->getPlatformRate();
        \Think\Log::write('奖励计算 - 平台费率: platformRate=' . $platformRate, 'INFO');

        // 计算平台服务费：max(0, 报名费 - service_price) * platform_rate
        $platformFee = max(0, $feeAmountYuan - $servicePrice) * $platformRate;
        \Think\Log::write('奖励计算 - 平台服务费: platformFee=' . $platformFee .
            ' (max(0, ' . $feeAmountYuan . ' - ' . $servicePrice . ') * ' . $platformRate . ')', 'INFO');

        // 新的奖励计算：服务站收益 = 报名费 - 项目身份成本 - 平台服务费
        $stationReward = $feeAmountYuan - $projectIdentityCost - $platformFee;
        \Think\Log::write('奖励计算 - 服务站收益: stationReward=' . $stationReward .
            ' (' . $feeAmountYuan . ' - ' . $projectIdentityCost . ' - ' . $platformFee . ')', 'INFO');

        // 确保服务站收益不为负数
        if ($stationReward < 0) {
            \Think\Log::write('奖励计算 - 警告：服务站收益为负数，设为0: 原值=' . $stationReward, 'WARN');
            $stationReward = 0;
        }

        // 上级服务站奖励已取消 - 统一设为0
        $parentReward = 0;
        \Think\Log::write('奖励计算 - 上级服务站奖励已取消，设为0', 'INFO');

        // 设置奖励金额（转换为分单位存储，数据库字段注释为分单位）
        $data['reward_station_amt'] = intval($stationReward * 100);
        $data['reward_parent_amt'] = intval($parentReward * 100);

        \Think\Log::write('奖励计算 - 最终结果: reward_station_amt=' . $data['reward_station_amt'] . '分(' . $stationReward . '元)' .
            ', reward_parent_amt=' . $data['reward_parent_amt'] . '分(' . $parentReward . '元)', 'INFO');
    }

    /**
     * 计算平台收入
     * @param array &$data 订单数据（引用传递，直接修改原数组）
     * @return void
     */
    private function calculatePlatformCommission(&$data)
    {
        try {
            // 获取岗位信息
            $post = D('ProjectPost')->where(['id' => $data['post_id']])->find();
            if (!$post) {
                \Think\Log::write('平台收入计算 - 岗位不存在: post_id=' . $data['post_id'], 'ERROR');
                $data['zcgk_commission'] = 0;
                return;
            }

            // 报名费（分单位转元单位）
            $feeAmountYuan = $data['fee_amount'] / 100;

            // 岗位对应的成本（使用internal_costs字段，元单位）
            $postCost = intval($post['internal_costs']);

            // 服务站收益计算
            $stationIncome = 0;

            if (!empty($data['zsb_id']) && $data['zsb_id'] > 0) {
                // 招就办订单：服务站收益 = 服务站收益 + 招就办收益
                $stationIncome = ($data['station_profit'] + $data['zsb_commission']) / 100; // 分转元
                \Think\Log::write('平台收入计算 - 招就办订单服务站收益: station_profit=' . $data['station_profit'] .
                    ', zsb_commission=' . $data['zsb_commission'] . ', total=' . $stationIncome . '元', 'INFO');
            } else {
                // 普通订单：服务站收益 = 服务站奖励金额
                $stationIncome = $data['reward_station_amt'] / 100; // 分转元
                \Think\Log::write('平台收入计算 - 普通订单服务站收益: reward_station_amt=' . $data['reward_station_amt'] .
                    ', income=' . $stationIncome . '元', 'INFO');
            }

            // 计算平台收入：报名费 - 岗位成本 - 服务站收益
            $platformCommission = $feeAmountYuan - $postCost - $stationIncome;

            // 确保平台收入不为负数
            if ($platformCommission < 0) {
                \Think\Log::write('平台收入计算 - 警告：平台收入为负数，设为0: 原值=' . $platformCommission .
                    ' (报名费=' . $feeAmountYuan . ', 岗位成本=' . $postCost . ', 服务站收益=' . $stationIncome . ')', 'WARN');
                $platformCommission = 0;
            }

            // 转换为分单位存储
            $data['zcgk_commission'] = intval($platformCommission * 100);

            \Think\Log::write('平台收入计算 - 最终结果: zcgk_commission=' . $data['zcgk_commission'] . '分(' . $platformCommission . '元)' .
                ' (报名费=' . $feeAmountYuan . '元, 岗位成本=' . $postCost . '元, 服务站收益=' . $stationIncome . '元)', 'INFO');

        } catch (\Exception $e) {
            \Think\Log::write('平台收入计算异常：' . $e->getMessage(), 'ERROR');
            $data['zcgk_commission'] = 0;
        }
    }

    /**
     * 获取平台费率配置
     * @return float 平台费率，默认0.30（30%）
     */
    private function getPlatformRate()
    {
        $conf = D('Conf')->where([
            'name' => 'platform_rate'
        ])->find();

        return $conf ? floatval($conf['value']) : 0.30;
    }

    /**
     * 更新订单状态（兼容原有方法）
     * @param int $id 订单ID
     * @param string $status 原有状态或新的小状态
     * @return bool 成功返回true，失败返回false
     */
    public function updateOrderStatus($id, $status)
    {
        // 兼容原有调用方式
        if (isset($this->order_status[$status])) {
            // 原有状态，映射到新的二级状态
            $mapping = [
                'new' => ['communication', 'initial_contact'],
                'pending_payment' => ['communication', 'contract_signed'],
                'paid' => ['communication', 'full_paid'],
                'training' => ['training', 'in_training'],
                'completed' => ['service_review', 'completed'],
                'closed' => ['service_review', 'terminated']
            ];

            if (isset($mapping[$status])) {
                return $this->updateOrderStatusV2($id, $mapping[$status][0], $mapping[$status][1]);
            }
        }

        return false;
    }

    /**
     * 根据sub_status获取对应的order_status
     * @param string $subStatus
     * @return string
     */
    private function getOrderStatusBySubStatus($subStatus)
    {
        $mapping = [
            'initial_contact' => 'new',
            'intent_paid' => 'new',
            'contract_signed' => 'pending_payment',
            'partial_paid' => 'paid',
            'full_paid' => 'paid',
            'pending_notice' => 'training',
            'notice_sent' => 'training',
            'docs_signed' => 'training',
            'submitted_institute' => 'training',
            'in_training' => 'training',
            'employed' => 'training',
            'confirming' => 'completed',
            'completed' => 'completed',
            'terminated' => 'closed'
        ];

        return $mapping[$subStatus] ?? 'new';
    }

    /**
     * 验证订单支付状态
     * @param int $orderId 订单ID
     * @return array ['is_paid' => bool, 'paid_amount' => int, 'remaining' => int, 'fee_amount' => int]
     */
    public function validatePaymentStatus($orderId)
    {
        // 获取订单信息（包含关联的项目和用户信息）
        $order = $this->alias('o')
            ->join('LEFT JOIN z_project_post p ON o.post_id = p.id')
            ->join('LEFT JOIN z_user u ON o.user_id = u.id')
            ->where(['o.id' => $orderId])
            ->field('o.*, p.job_name as project_name, u.nickname as user_name')
            ->find();

        if (!$order) {
            return ['error' => '订单不存在'];
        }

        // 计算已支付金额（排除退款）
        $paidAmount = D('PaymentRecord')->where([
            'order_id' => $orderId,
            'pay_type' => ['IN', ['intent','partial','full']]
        ])->sum('pay_amount');

        $paidAmount = intval($paidAmount); // 确保为整数
        $remaining = $order['fee_amount'] - $paidAmount;

        // 确保订单信息包含必要的显示字段
        $order['project_name'] = $order['project_name'] ?: '未知项目';
        $order['user_name'] = $order['user_name'] ?: '未知学员';

        return [
            'is_paid' => $remaining <= 0,
            'paid_amount' => $paidAmount,
            'remaining' => max(0, $remaining),
            'fee_amount' => $order['fee_amount'],
            'order_info' => $order
        ];
    }

    /**
     * 判断状态跳转是否需要支付验证
     * @param array $currentOrder 当前订单信息
     * @param string $targetSubStatus 目标子状态
     * @return bool
     */
    public function needsPaymentValidation($currentOrder, $targetSubStatus)
    {
        // 定义需要付款才能进入的状态（培训流程及后续所有状态）
        $paidRequiredStatuses = [
            // 培训流程
            'pending_notice',        // 待通知培训
            'notice_sent',          // 已通知培训
            'docs_signed',          // 已签署文件
            'submitted_institute',  // 已提交至培训机构
            'in_training',          // 参与培训中
            'end_training',          // 培训结束

            // 入职流程
            'waiting_examination',  // 待通知笔试
            'note_examination',     // 已通知笔试
            'in_examinationloyed',  // 已参加笔试
            'waiting_interview',    // 待通知面试
            'note_interview',       // 已通知面试
            'int_interviewyed',     // 已参加面试
            'waiting_notice',       // 等待入职通知
            'employed',             // 已入职

            // 服务审查
            'confirming',           // 服务完成确认中
            'completed'             // 服务完成
        ];

        // 定义付款前状态（这些状态下还没有付款，不应该触发付款验证）
        $prePaymentStatuses = [
            'initial_contact',      // 初步沟通
            'intent_paid',          // 已付意向金
            'contract_signed'       // 已签合同待付款
        ];

        // 特殊处理：从付款前状态直接跳转到"服务终止"不需要付款验证
        if ($targetSubStatus === 'terminated' && in_array($currentOrder['sub_status'], $prePaymentStatuses)) {
            return false;
        }

        // 定义已付款状态（包括full_paid和所有培训流程状态）
        $paidStatuses = array_merge(['full_paid'], $paidRequiredStatuses);

        // 如果目标状态是需要付款的状态，且当前状态不是已付款状态，则需要验证
        if (in_array($targetSubStatus, $paidRequiredStatuses) && !in_array($currentOrder['sub_status'], $paidStatuses)) {
            return true;
        }

        return false;
    }

    /**
     * 判断状态跳转是否涉及资金操作
     * @param string $subStatus 目标子状态
     * @return array ['has_fund_operation' => bool, 'operation_type' => string, 'description' => string]
     */
    public function getFundOperationInfo($subStatus)
    {
        $fundOperations = [
            'full_paid' => [
                'has_fund_operation' => true,
                'operation_type' => 'freeze',
                'description' => '冻结服务站资金'
            ],
            'completed' => [
                'has_fund_operation' => true,
                'operation_type' => 'unfreeze',
                'description' => '解冻服务站资金并转为可提现'
            ],
            'terminated' => [
                'has_fund_operation' => true,
                'operation_type' => 'refund',
                'description' => '扣除冻结资金'
            ]
        ];

        return $fundOperations[$subStatus] ?? ['has_fund_operation' => false];
    }

    /**
     * 计算资金操作金额
     * @param array $order 订单信息
     * @param string $subStatus 目标状态
     * @return int 资金金额（分单位）
     */
    public function calculateFundAmount($order, $subStatus)
    {
        switch ($subStatus) {
            case 'full_paid':
                // 冻结金额
                if (!empty($order['zsb_id']) && $order['zsb_id'] > 0) {
                    // 招就办订单：服务站收益 + 招就办收益
                    return ($order['station_profit'] + $order['zsb_commission']);
                } else {
                    // 普通订单：服务站奖励金额
                    return $order['reward_station_amt'];
                }
                break;
            case 'completed':
                // 解冻金额：只有当订单真的有冻结资金时才返回金额
                if ($this->hasFreezedReward($order['id'])) {
                    // 有冻结资金，返回与冻结金额相同的解冻金额
                    return $this->calculateFundAmount($order, 'full_paid');
                } else {
                    // 没有冻结资金，无需解冻操作
                    return 0;
                }
                break;

            case 'terminated':
                // 服务终止：退款金额（不是解冻，是退回给学员）
                if ($this->hasFreezedReward($order['id'])) {
                    // 有冻结资金，返回需要退款的金额
                    return $this->calculateFundAmount($order, 'full_paid');
                } else {
                    // 没有冻结资金，无需退款操作
                    return 0;
                }
                break;
        }

        return 0;
    }

    /**
     * 更新订单状态（新版本）
     * @param int $orderId 订单ID
     * @param string $mainStatus 大状态
     * @param string $subStatus 小状态
     * @param array $amountInfo 金额信息
     * @param int $operatorId 操作人ID（管理员ID）
     * @param string $remark 备注
     * @param string $payChannel 支付方式
     * @param bool $skipPaymentValidation 是否跳过支付验证（仅超级管理员）
     * @return bool|array
     */
    public function updateOrderStatusV2($orderId, $mainStatus, $subStatus, $amountInfo = [], $operatorId = 0, $remark = '', $payChannel = 'manual', $skipPaymentValidation = false)
    {
        // 【调试】记录参数信息
        \Think\Log::write('updateOrderStatusV2调用参数 order_id=' . $orderId . ' skipPaymentValidation=' . ($skipPaymentValidation ? 'true' : 'false'), 'INFO');

        // 获取当前订单信息
        $currentOrder = $this->where(['id' => $orderId])->find();
        if (!$currentOrder) {
            \Think\Log::write('订单状态更新失败：订单不存在 order_id=' . $orderId, 'ERROR');
            return false;
        }

        // 验证状态转换是否合法（只有管理员可以操作）
        if (!$this->validateStatusTransition($currentOrder, $mainStatus, $subStatus, $operatorId)) {
            \Think\Log::write($operatorId . '订单状态更新失败：状态转换不合法或权限不足 from=' . ($currentOrder['sub_status'] ?? 'null') . ' to=' . $subStatus, 'ERROR');
            return false;
        }

        // 资金安全验证：检查是否需要支付验证
        if (!$skipPaymentValidation && $this->needsPaymentValidation($currentOrder, $subStatus)) {
            $paymentStatus = $this->validatePaymentStatus($orderId);
            if (isset($paymentStatus['error'])) {
                \Think\Log::write('订单状态更新失败：' . $paymentStatus['error'], 'ERROR');
                return false;
            }

            if (!$paymentStatus['is_paid']) {
                // 返回特殊状态码，前端处理付款确认
                \Think\Log::write('订单状态更新拦截：需要付款确认 order_id=' . $orderId . ' remaining=' . $paymentStatus['remaining'], 'INFO');
                return [
                    'status' => 'payment_required',
                    'payment_info' => $paymentStatus,
                    'target_status' => $mainStatus . '.' . $subStatus,
                    'remark' => $remark
                ];
            }
        }

        // 检查资金操作提醒
        $fundInfo = $this->getFundOperationInfo($subStatus);
        if ($fundInfo['has_fund_operation'] && !$skipPaymentValidation) {
            // 计算资金操作金额
            $fundAmount = $this->calculateFundAmount($currentOrder, $subStatus);
            if ($fundAmount > 0) {
                // 获取完整的订单信息（包含关联数据）
                $orderInfoWithRelations = $this->getOrderWithRelations($orderId);

                return [
                    'status' => 'fund_operation_confirm',
                    'fund_info' => array_merge($fundInfo, [
                        'amount' => $fundAmount,
                        'order_info' => $orderInfoWithRelations
                    ]),
                    'target_status' => $mainStatus . '.' . $subStatus,
                    'remark' => $remark
                ];
            }
        }

        // 开启事务
        $this->startTrans();

        try {
            // 确保主状态与子状态一致，如果提供的主状态与子状态不匹配，则使用子状态对应的主状态
            $correctMainStatus = $this->getMainStatusBySubStatus($subStatus);
            if ($mainStatus !== $correctMainStatus) {
                \Think\Log::write('主状态与子状态不匹配，自动修正 main_status=' . $mainStatus . ' → ' . $correctMainStatus . ' sub_status=' . $subStatus, 'INFO');
                $mainStatus = $correctMainStatus;
            }
            
            // 构建更新数据
            $updateData = [
                'main_status' => $mainStatus,
                'sub_status' => $subStatus,
                'status_desc' => $this->sub_status[$subStatus]['text'] ?? $subStatus,
                'status_update_time' => time(),
                'status_operator_id' => $operatorId, // 管理员ID
                'status_remark' => $remark,
                'update_time' => time()
            ];

            // 手动同步order_status（因为触发器可能不存在）
            $updateData['order_status'] = $this->getOrderStatusBySubStatus($subStatus);

            // 处理金额信息（创建付款记录，但不修改订单表的fee_amount）
            if (!empty($amountInfo)) {
                // 注意：不再更新订单表的fee_amount，保持报名费不变
                // 报名费应该在订单创建时确定，不应该因为支付金额而改变

                // 创建付款记录
                $this->createPaymentRecord($orderId, $subStatus, $amountInfo, $payChannel);
            } elseif ($subStatus == 'full_paid') {
                // 【修复】当跳转到全额付款状态时，即使没有传入金额信息，也要创建支付记录
                // 这种情况通常发生在管理员直接跳转订单状态时
                \Think\Log::write('全额付款状态跳转：未传入金额信息，自动创建支付记录 order_id=' . $orderId . ' pay_channel=' . $payChannel, 'INFO');
                $this->createPaymentRecord($orderId, $subStatus, [], $payChannel);
            }

            // 执行更新（触发器会自动同步order_status等字段）
            $result = $this->where(['id' => $orderId])->save($updateData);
            if ($result === false) {
                throw new \Exception('订单状态更新失败');
            }

            // 记录状态变更日志（完整版）
            $this->recordStatusChange($orderId, $currentOrder, $mainStatus, $subStatus, $operatorId, $remark, $amountInfo);

            // 【修复】处理状态相关的业务逻辑 - 在事务提交前执行，确保原子性
            $this->handleStatusBusinessLogic($orderId, $subStatus, $amountInfo);

            // 【修复】所有操作成功后才提交事务，确保订单状态更新和业务逻辑处理的原子性
            $this->commit();
            \Think\Log::write('订单状态更新成功 order_id=' . $orderId . ' status=' . $subStatus, 'INFO');
            return true;

        } catch (\Exception $e) {
            $this->rollback();
            \Think\Log::write('订单状态更新异常: ' . $e->getMessage(), 'ERROR');
            return false;
        }
    }

    /**
     * 创建付款记录
     */
    private function createPaymentRecord($orderId, $subStatus, $amountInfo, $payChannel = 'manual')
    {
        $paymentModel = D('PaymentRecord');

        // 根据状态确定支付类型和金额
        $payType = 'full';
        $payAmount = 0;

        switch ($subStatus) {
            case 'intent_paid':
                $payType = 'intent';
                $payAmount = $amountInfo['intent_amount'] ?? 0;
                break;
            case 'partial_paid':
                $payType = 'partial';
                $payAmount = $amountInfo['partial_amount'] ?? 0;
                break;
            case 'full_paid':
                $payType = 'full';
                // 修复：全额付款时，计算剩余未付金额而不是使用订单总金额
                if (isset($amountInfo['fee_amount']) && $amountInfo['fee_amount'] > 0) {
                    // 如果传入了具体金额，使用传入的金额
                    $payAmount = $amountInfo['fee_amount'];
                } else {
                    // 否则计算剩余未付金额
                    $order = $this->where(['id' => $orderId])->find();
                    if ($order) {
                        // 获取已支付金额
                        $paidAmount = $paymentModel->where([
                            'order_id' => $orderId,
                            'pay_type' => ['in', ['intent', 'partial']]
                        ])->sum('pay_amount') ?: 0;

                        // 计算剩余未付金额（分单位）
                        $remainingAmount = $order['fee_amount'] - $paidAmount;
                        $payAmount = max(0, $remainingAmount / 100); // 转换为元单位，确保不为负数

                        \Think\Log::write("全额付款计算: 订单总额={$order['fee_amount']}分, 已付={$paidAmount}分, 剩余={$remainingAmount}分, 本次付款={$payAmount}元", 'INFO');
                    } else {
                        $payAmount = 0;
                    }
                }
                break;
        }

        if ($payAmount > 0) {
            $paymentData = [
                'order_id' => $orderId,
                'pay_channel' => $payChannel, // 使用传入的支付方式
                'pay_amount' => intval($payAmount * 100), // 转换为分单位
                'pay_type' => $payType,
                'pay_time' => time(),
                'remark' => '状态变更自动创建付款记录',
                'create_time' => time()
            ];

            $paymentId = $paymentModel->add($paymentData);
            \Think\Log::write('创建付款记录 order_id=' . $orderId . ' payment_id=' . $paymentId . ' amount=' . $payAmount . ' type=' . $payType . ' channel=' . $payChannel, 'INFO');

            return $paymentId;
        }

        return false;
    }

    /**
     * 获取订单的分阶段付款金额
     */
    public function getOrderPaymentAmounts($orderId)
    {
        $paymentModel = D('PaymentRecord');

        // 获取意向金总额
        $intentAmount = $paymentModel->where([
            'order_id' => $orderId,
            'pay_type' => 'intent'
        ])->sum('pay_amount') ?: 0;

        // 获取部分付款总额
        $partialAmount = $paymentModel->where([
            'order_id' => $orderId,
            'pay_type' => 'partial'
        ])->sum('pay_amount') ?: 0;

        // 获取全额付款总额
        $fullAmount = $paymentModel->where([
            'order_id' => $orderId,
            'pay_type' => 'full'
        ])->sum('pay_amount') ?: 0;

        // 获取总付款金额
        $totalPaid = $paymentModel->where([
            'order_id' => $orderId,
            'pay_type' => ['in', ['intent', 'partial', 'full']]
        ])->sum('pay_amount') ?: 0;

        return [
            'intent_amount' => $intentAmount, // 分单位
            'partial_amount' => $partialAmount, // 分单位
            'full_amount' => $fullAmount, // 分单位
            'total_paid' => $totalPaid, // 分单位
            'intent_amount_yuan' => round($intentAmount / 100, 2), // 元单位
            'partial_amount_yuan' => round($partialAmount / 100, 2), // 元单位
            'full_amount_yuan' => round($fullAmount / 100, 2), // 元单位
            'total_paid_yuan' => round($totalPaid / 100, 2) // 元单位
        ];
    }

    /**
     * 记录状态变更（完整版）
     */
    private function recordStatusChange($orderId, $currentOrder, $mainStatus, $subStatus, $operatorId, $remark, $amountInfo = [])
    {
        // 获取管理员信息
        $operatorName = '';
        $operatorType = 'admin';
        if ($operatorId > 0) {
            $operator = D('Users')->where(['id' => $operatorId])->find();
            $operatorName = $operator ? $operator['username'] : '';
        } else {
            $operatorType = 'system';
            $operatorName = '系统';
        }

        // 构建日志信息
        $logInfo = [
            'order_id' => $orderId,
            'from_status' => ($currentOrder['main_status'] ?? '') . '.' . ($currentOrder['sub_status'] ?? ''),
            'to_status' => $mainStatus . '.' . $subStatus,
            'operator_id' => $operatorId,
            'operator_name' => $operatorName,
            'remark' => $remark,
            'ip' => get_client_ip(),
            'time' => date('Y-m-d H:i:s')
        ];

        // 写入系统日志
        \Think\Log::write('培训订单状态变更: ' . json_encode($logInfo, JSON_UNESCAPED_UNICODE), 'INFO');

        // 写入数据库状态历史表
        try {
            $historyModel = M('training_order_status_history');
            $historyData = [
                'order_id' => $orderId,
                'from_main_status' => $currentOrder['main_status'] ?? null,
                'from_sub_status' => $currentOrder['sub_status'] ?? null,
                'to_main_status' => $mainStatus,
                'to_sub_status' => $subStatus,
                'status_desc' => $this->sub_status[$subStatus]['text'] ?? $subStatus,
                'amount_info' => !empty($amountInfo) ? json_encode($amountInfo, JSON_UNESCAPED_UNICODE) : null,
                'operator_id' => $operatorId,
                'operator_type' => $operatorType,
                'operator_name' => $operatorName,
                'remark' => $remark,
                'ip_address' => get_client_ip(),
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                'create_time' => time()
            ];

            $result = $historyModel->add($historyData);
            if ($result) {
                \Think\Log::write('状态历史记录写入成功 order_id=' . $orderId . ' history_id=' . $result, 'INFO');
            } else {
                \Think\Log::write('状态历史记录写入失败 order_id=' . $orderId, 'ERROR');
            }
        } catch (\Exception $e) {
            \Think\Log::write('状态历史记录写入异常: ' . $e->getMessage(), 'ERROR');
        }
    }

    /**
     * 为现有订单补充初始状态历史记录
     */
    public function createInitialStatusHistory($orderId)
    {
        $order = $this->where(['id' => $orderId])->find();
        if (!$order) {
            return false;
        }

        // 检查是否已有历史记录
        $historyModel = M('training_order_status_history');
        $existingCount = $historyModel->where(['order_id' => $orderId])->count();

        if ($existingCount > 0) {
            return true; // 已有记录，不需要创建
        }

        // 创建初始状态记录
        try {
            $historyData = [
                'order_id' => $orderId,
                'from_main_status' => null,
                'from_sub_status' => null,
                'to_main_status' => $order['main_status'] ?: 'communication',
                'to_sub_status' => $order['sub_status'] ?: 'initial_contact',
                'status_desc' => '订单创建',
                'amount_info' => null,
                'operator_id' => 0,
                'operator_type' => 'system',
                'operator_name' => '系统',
                'remark' => '订单创建时的初始状态',
                'ip_address' => '',
                'user_agent' => '',
                'create_time' => $order['create_time']
            ];

            $result = $historyModel->add($historyData);
            if ($result) {
                \Think\Log::write('初始状态历史记录创建成功 order_id=' . $orderId, 'INFO');
                return true;
            }
        } catch (\Exception $e) {
            \Think\Log::write('初始状态历史记录创建失败: ' . $e->getMessage(), 'ERROR');
        }

        return false;
    }

    /**
     * 验证状态转换是否合法
     */
    private function validateStatusTransition($currentOrder, $newMainStatus, $newSubStatus, $operatorId)
    {
        $currentSub = $currentOrder['sub_status'] ?? 'initial_contact';
        $sessionAdminId = session('admin_id');

        // 安全的权限验证逻辑

        // 1. 系统自动调用（operatorId = 0）：只允许特定的自动状态转换
        if ($operatorId === 0) {
            $allowedAutoTransitions = ['confirming']; // 只允许自动进入确认状态
            if (in_array($newSubStatus, $allowedAutoTransitions)) {
                \Think\Log::write('系统自动状态转换 from=' . $currentSub . ' to=' . $newSubStatus, 'INFO');
                return true;
            } else {
                \Think\Log::write('非法的系统自动状态转换 from=' . $currentSub . ' to=' . $newSubStatus, 'ERROR');
                return false;
            }
        }

        // 2. 管理员操作：必须同时满足以下条件
        // - operatorId > 0 且与当前session中的admin_id一致
        // - 或者当前session中有有效的admin_id（兼容某些调用方式）
        $isValidAdmin = false;
        \Think\Log::write('sessionAdminId:' . $sessionAdminId . ' operatorId:' . $operatorId, 'INFO');
        if ($operatorId > 0 && $sessionAdminId && $operatorId == $sessionAdminId) {
            // 最安全：operatorId与session一致
            $isValidAdmin = true;
            \Think\Log::write('验证通过:' . $sessionAdminId . ' operatorId:' . $operatorId, 'INFO');
        } elseif ($operatorId > 0 && $sessionAdminId) {
            // 验证operatorId是否为有效的管理员
            $adminUser = M('Users')->where(['id' => $operatorId, 'status' => 1])->find();
            if ($adminUser) {
                $isValidAdmin = true;
                \Think\Log::write('验证管理员身份通过 operator_id=' . $operatorId, 'INFO');
            }
        } elseif ($sessionAdminId && $operatorId === 0) {
            // 兼容某些调用方式：session有admin_id但operatorId为0
            $isValidAdmin = true;
            \Think\Log::write('基于session验证管理员权限 admin_id=' . $sessionAdminId, 'INFO');
        }

        if (!$isValidAdmin) {
            \Think\Log::write('权限验证失败 operator_id=' . $operatorId . ' session_admin_id=' . $sessionAdminId, 'ERROR');
            return false;
        }

        // 3. 特殊权限：超级管理员可以执行任何状态转换
        if ($sessionAdminId == 1) {
            \Think\Log::write('超级管理员执行状态转换 from=' . $currentSub . ' to=' . $newSubStatus, 'WARN');
            return true;
        }

        // 4. 特殊情况：管理员可以强制转换到 terminated 状态（取消订单）
        if ($newSubStatus === 'terminated') {
            \Think\Log::write('管理员取消订单 from=' . $currentSub . ' to=' . $newSubStatus . ' admin_id=' . $sessionAdminId, 'INFO');
            return true;
        }

        // 5. 常规状态转换验证
        $currentStatusConfig = $this->sub_status[$currentSub] ?? [];
        // 下一步允许的状态为当前状态优先级高的状态
        // 管理员可以直接跳转到任何更高优先级的状态
        $allowedNextActions = $this->getHigherPriorityStatuses($currentSub);
        
        if (!in_array($newSubStatus, $allowedNextActions)) {
            \Think\Log::write('状态转换不合法 from=' . $currentSub . ' to=' . $newSubStatus, 'ERROR');
            return false;
        }

        \Think\Log::write('管理员状态转换验证通过 from=' . $currentSub . ' to=' . $newSubStatus . ' admin_id=' . $sessionAdminId, 'INFO');
        return true;
    }

    /**
     * 获取当前状态的所有更高优先级状态
     * 允许管理员跳转到任何更高优先级的状态
     * @param string $currentStatus 当前状态
     * @return array 允许跳转的状态列表
     */
    private function getHigherPriorityStatuses($currentStatus)
    {
        // 定义各个主状态下的子状态及其优先级（数字越大表示优先级越高）
        $statusPriority = [
            // 沟通流程
            'initial_contact' => 10,
            'intent_paid' => 20,
            'contract_signed' => 30,
            'partial_paid' => 40,
            'full_paid' => 50,
            
            // 培训流程
            'pending_notice' => 60,
            'notice_sent' => 70,
            'docs_signed' => 80,
            'submitted_institute' => 90,
            'in_training' => 100,
            'end_training' => 110,
            
            // 入职流程
            'waiting_examination' => 120,
            'note_examination' => 130,
            'in_examinationloyed' => 140,
            'waiting_interview' => 150,
            'note_interview' => 160,
            'int_interviewyed' => 170,
            'waiting_notice' => 180,
            'employed' => 190,
            
            // 服务审查
            'confirming' => 200,
            'completed' => 210
            // 不包含terminated，因为它是特殊状态，可以从任何状态直接转换
        ];
        
        // 如果当前状态不在优先级列表中，则返回其next_actions
        if (!isset($statusPriority[$currentStatus])) {
            $currentConfig = $this->sub_status[$currentStatus] ?? [];
            return $currentConfig['next_actions'] ?? [];
        }
        
        $currentPriority = $statusPriority[$currentStatus];
        $allowedStatuses = [];
        
        // 获取所有优先级更高的状态
        foreach ($statusPriority as $status => $priority) {
            if ($priority > $currentPriority) {
                $allowedStatuses[] = $status;
            }
        }
        
        // 始终允许转换为terminated状态
        $allowedStatuses[] = 'terminated';
        
        // 记录日志
        \Think\Log::write('计算状态 ' . $currentStatus . ' 的更高优先级状态: ' . implode(', ', $allowedStatuses), 'INFO');
        
        return $allowedStatuses;
    }
    
    /**
     * 获取子状态对应的主状态
     * @param string $subStatus 子状态
     * @return string 主状态
     */
    private function getMainStatusBySubStatus($subStatus)
    {
        if (isset($this->sub_status[$subStatus])) {
            return $this->sub_status[$subStatus]['main'];
        }
        
        // 默认映射关系，确保状态一致性
        $defaultMapping = [
            // 沟通流程
            'initial_contact' => 'communication',
            'intent_paid' => 'communication',
            'contract_signed' => 'communication',
            'partial_paid' => 'communication',
            'full_paid' => 'communication',
            
            // 培训流程
            'pending_notice' => 'training',
            'notice_sent' => 'training',
            'docs_signed' => 'training',
            'submitted_institute' => 'training',
            'in_training' => 'training',
            'end_training' => 'training',
            
            // 入职流程
            'waiting_examination' => 'employment',
            'note_examination' => 'employment',
            'in_examinationloyed' => 'employment',
            'waiting_interview' => 'employment',
            'note_interview' => 'employment',
            'int_interviewyed' => 'employment',
            'waiting_notice' => 'employment',
            'employed' => 'employment',
            
            // 服务审查
            'confirming' => 'service_review',
            'completed' => 'service_review',
            'terminated' => 'service_review'
        ];
        
        return $defaultMapping[$subStatus] ?? 'communication';
    }

    /**
     * 处理状态相关的业务逻辑
     */
    private function handleStatusBusinessLogic($orderId, $subStatus, $amountInfo)
    {
        $statusConfig = $this->sub_status[$subStatus] ?? [];
        $order = $this->where(['id' => $orderId])->find();

        // 【新逻辑】根据一级状态统一同步用户状态
        $this->syncUserStatusByMainStatus($order['user_id'], $order['main_status'], $subStatus, $order['user_job_id'] ?? null, $order);

        // 处理特殊业务逻辑
        switch ($subStatus) {
            case 'full_paid':
                // 全额付款后冻结资金（使用正确的资金记录类型）
                $this->freezeOrderReward($orderId, $order);
                break;

            case 'pending_notice':
            case 'notice_sent':
            case 'docs_signed':
            case 'submitted_institute':
            case 'in_training':
            case 'end_training':        // 【修复：添加培训结束状态】
            case 'waiting_examination': // 【修复：添加入职流程状态】
            case 'note_examination':
            case 'in_examinationloyed':
            case 'waiting_interview':
            case 'note_interview':
            case 'int_interviewyed':
            case 'waiting_notice':
            case 'confirming':
                // 从"已全额收款"状态跳转到后续状态时，如果还没有冻结资金，则执行冻结
                if (!$this->hasFreezedReward($orderId)) {
                    \Think\Log::write('状态跳转时补充冻结资金 order_id=' . $orderId . ' status=' . $subStatus, 'INFO');
                    $this->freezeOrderReward($orderId, $order);
                }
                break;

            case 'employed':
                // 从"已全额收款"状态跳转到后续状态时，如果还没有冻结资金，则执行冻结
                if (!$this->hasFreezedReward($orderId)) {
                    \Think\Log::write('状态跳转时补充冻结资金 order_id=' . $orderId . ' status=' . $subStatus, 'INFO');
                    $this->freezeOrderReward($orderId, $order);
                }

                // 入职后自动进入服务确认（如果配置了auto_next）
                if ($statusConfig['auto_next'] ?? false) {
                    // 延迟执行，避免递归调用
                    $this->addDelayedTask('updateOrderStatusV2', [$orderId, 'service_review', 'confirming', [], 0, '系统自动进入服务完成确认']);
                }
                break;

            case 'completed':
                // 【修复】服务完成前必须先确保资金已冻结，然后才能解冻
                // 检查是否有冻结资金，如果没有则先冻结
                if (!$this->hasFreezedReward($orderId)) {
                    \Think\Log::write('completed状态：订单无冻结资金，先执行冻结 order_id=' . $orderId, 'INFO');
                    $this->freezeOrderReward($orderId, $order);
                }

                // 服务完成后解冻并分发资金（复用现有逻辑）
                if (!empty($order['zsb_id']) && $order['zsb_id'] > 0) {
                    // 招就办订单完成处理
                    $this->completeZsbOrderV2($orderId);
                } else {
                    // 普通订单完成处理
                    $this->unlockReward($orderId, 'completed');
                }
                break;

            case 'terminated':
                // 服务终止后处理退款（复用现有逻辑）
                $this->handleCancelRefund($orderId, $order);
                break;
        }
    }

    /**
     * 根据一级状态统一同步用户状态
     * @param int $userId 用户ID
     * @param string $mainStatus 订单一级状态
     * @param string $subStatus 订单二级状态
     * @param int|null $userJobId 用户简历ID
     * @param array $orderInfo 完整订单信息（用于判断终止原因）
     */
    private function syncUserStatusByMainStatus($userId, $mainStatus, $subStatus, $userJobId = null, $orderInfo = [])
    {
        if (empty($userId)) {
            return;
        }

        // 获取当前用户状态
        $currentUser = D('User')->where(['id' => $userId])->find();
        if (!$currentUser) {
            return;
        }

        // 根据一级状态确定目标用户状态
        $targetUserStatus = $this->getTargetUserStatusByMainStatus($mainStatus, $subStatus, $orderInfo);

        if ($targetUserStatus === null) {
            return; // 不需要更新
        }

        // 状态升级保护：不降级已入职用户（除非是terminated）
        if ($currentUser['service_status'] == 2 && $targetUserStatus < 2 && $subStatus !== 'terminated') {
            \Think\Log::write('用户状态保护：用户已入职，跳过降级 user_id=' . $userId . ' current=2 target=' . $targetUserStatus, 'INFO');
            return;
        }

        // 更新用户状态
        if ($currentUser['service_status'] != $targetUserStatus) {
            $updateResult = D('User')->where(['id' => $userId])->save(['service_status' => $targetUserStatus]);
            if ($updateResult !== false) {
                \Think\Log::write('用户状态同步成功 user_id=' . $userId . ' main_status=' . $mainStatus . ' sub_status=' . $subStatus . ' ' . $currentUser['service_status'] . '→' . $targetUserStatus, 'INFO');

                // 同步简历状态
                $this->syncUserJobStatus($userId, $userJobId, $targetUserStatus);
            } else {
                \Think\Log::write('用户状态同步失败 user_id=' . $userId . ' target_status=' . $targetUserStatus, 'ERROR');
            }
        }
    }

    /**
     * 根据一级状态和二级状态确定目标用户状态
     * @param string $mainStatus 一级状态
     * @param string $subStatus 二级状态
     * @param array $orderInfo 完整订单信息（用于判断终止原因）
     * @return int|null 目标用户状态，null表示不需要更新
     */
    private function getTargetUserStatusByMainStatus($mainStatus, $subStatus, $orderInfo = [])
    {
        // 特殊处理：terminated状态需要根据终止原因细分
        if ($subStatus === 'terminated') {
            return $this->getTerminatedUserStatus($orderInfo);
        }

        // 就业流程细化：只有“已入职”以及服务审查阶段才视为已入职
        if ($mainStatus === 'employment') {
            if ($subStatus === 'employed') {
                return 2; // 已入职
            }
            // 其余入职流程子状态（如 waiting_examination/note_examination/in_examinationloyed/
            // waiting_interview/note_interview/int_interviewyed/waiting_notice 等）均视为培训中
            return 1; // 培训中
        }

        // 服务审查阶段：按已入职处理（confirming/completed 等）
        if ($mainStatus === 'service_review') {
            return 2; // 已入职（服务审查阶段）
        }

        // 其他主状态的常规映射
        $statusMapping = [
            'communication' => 0,    // 沟通中
            'training' => 1          // 培训中
        ];

        return isset($statusMapping[$mainStatus]) ? $statusMapping[$mainStatus] : null;
    }

    /**
     * 根据终止原因确定用户状态
     * @param array $orderInfo 订单信息
     * @return int 用户状态：0=沟通中（允许重新报名），3=服务终止（标记服务问题）
     */
    private function getTerminatedUserStatus($orderInfo = [])
    {
        // 获取状态变更备注
        $statusRemark = $orderInfo['status_remark'] ?? '';

        // 判断是否为取消报名导致的服务终止
        $cancelKeywords = [
            '取消订单',
            '审核驳回',
            '管理员取消',
            '取消报名',
            '驳回申请',
            '不符合要求',
            '资料不全'
        ];

        foreach ($cancelKeywords as $keyword) {
            if (strpos($statusRemark, $keyword) !== false) {
                \Think\Log::write('terminated状态判断：取消报名类型，设置用户状态为沟通中 order_id=' . ($orderInfo['id'] ?? 'unknown') . ' remark=' . $statusRemark, 'INFO');
                return 0; // 沟通中，允许重新报名
            }
        }

        // 其他情况视为服务质量问题导致的服务终止
        \Think\Log::write('terminated状态判断：服务质量问题类型，设置用户状态为服务终止 order_id=' . ($orderInfo['id'] ?? 'unknown') . ' remark=' . $statusRemark, 'INFO');
        return 3; // 服务终止，标记服务问题
    }

    /**
     * 同步用户简历状态
     * @param int $userId 用户ID
     * @param int|null $userJobId 特定简历ID
     * @param int $targetStatus 目标状态
     */
    private function syncUserJobStatus($userId, $userJobId, $targetStatus)
    {
        if (!empty($userJobId)) {
            // 如果订单关联了特定简历，只更新该简历状态
            D('UserJob')->where(['id' => $userJobId])->save(['job_state' => $targetStatus]);
        } else {
            // 如果没有关联简历，更新该用户所有简历的状态
            $userInfo = D('User')->where(['id' => $userId])->find();
            if ($userInfo && !empty($userInfo['mobile'])) {
                D('UserJob')->where(['phone' => $userInfo['mobile']])->save(['job_state' => $targetStatus]);
            }
        }
    }

    /**
     * 获取包含关联信息的完整订单数据
     * @param int $orderId 订单ID
     * @return array 包含关联信息的订单数据
     */
    private function getOrderWithRelations($orderId)
    {
        // 获取基本订单信息
        $order = $this->where(['id' => $orderId])->find();
        if (!$order) {
            return [];
        }

        // 获取用户信息
        $user = D('User')->where(['id' => $order['user_id']])->find();

        // 如果订单关联了简历，优先使用简历信息
        if (!empty($order['user_job_id'])) {
            $userJob = D('UserJob')->where(['id' => $order['user_job_id']])->find();
            if ($userJob) {
                $order['user_name'] = $userJob['name'];
            }
        } else if ($user) {
            $order['user_name'] = $user['nickname'];
        } else {
            $order['user_name'] = '未知学员';
        }

        // 获取项目信息
        $post = D('ProjectPost')->where(['id' => $order['post_id']])->find();
        if ($post) {
            $project = D('Project')->where(['id' => $post['project_id']])->find();
            if ($project) {
                $order['project_name'] = $project['name'];
            } else {
                $order['project_name'] = $post['job_name'] ?: '未知项目';
            }
        } else {
            $order['project_name'] = '未知项目';
        }

        return $order;
    }

    /**
     * 冻结订单奖励资金
     * @param int $orderId 订单ID
     * @param array $order 订单信息
     */
    private function freezeOrderReward($orderId, $order)
    {
        if (!empty($order['zsb_id']) && $order['zsb_id'] > 0) {
            // 招就办订单：服务站和招就办分别冻结对应的收益

            // 1. 服务站冻结总收益（服务站收益 + 招就办收益）
            $totalReward = ($order['station_profit'] + $order['zsb_commission']); // 已经是分单位
            $this->freezeStationAmountV2($order['station_id'], $totalReward, $orderId, '招就办培训订单总收益冻结', 5);

            // 2. 招就办冻结招就办收益
            if ($order['zsb_commission'] > 0) {
                $this->freezeZsbCommissionV2($order['zsb_id'], $order['zsb_commission'], $orderId);
            }
        } else {
            // 普通订单：冻结服务站奖励金额
            $freezeAmount = $order['reward_station_amt']; // 已经是分单位
            $this->freezeStationAmountV2($order['station_id'], $freezeAmount, $orderId, '培训订单资金冻结', 5);
        }

        \Think\Log::write('订单资金冻结完成 order_id=' . $orderId . ' zsb_id=' . ($order['zsb_id'] ?? 0), 'INFO');
    }

    /**
     * 检查订单是否已经冻结过资金
     * @param int $orderId 订单ID
     * @return bool
     */
    private function hasFreezedReward($orderId)
    {
        // 检查z_station_money表中是否有冻结记录
        $stationMoneyModel = D('StationMoney');
        $freezeRecord = $stationMoneyModel->where([
            'remark' => ['like', '%订单:' . $orderId . '%'],
            'type' => ['in', [5, 13]] // 5=培训奖励(冻结), 13=招就办佣金(冻结)
        ])->find();

        return !empty($freezeRecord);
    }

    /**
     * 添加延迟任务（简单实现）
     */
    private function addDelayedTask($method, $params)
    {
        // 简单的延迟任务实现，可以后续优化为队列
        register_shutdown_function(function() use ($method, $params) {
            if (method_exists($this, $method)) {
                call_user_func_array([$this, $method], $params);
            }
        });
    }

    /**
     * 冻结服务站资金（旧版本，使用错误的type=1）
     */
    private function freezeStationAmount($stationId, $amount, $orderId, $remark)
    {
        // 基于现有z_station_money表结构的资金冻结逻辑
        // type: 1=收入, 0=支出
        // status: 1=正常, 0=冻结
        // money: 金额(元单位) - z_station_money表使用元单位存储

        // 将分单位转换为元单位（输入的amount是分单位）
        $amountYuan = round($amount / 100, 2);

        $stationMoneyModel = D('StationMoney');
        $freezeData = [
            'service_station_id' => $stationId,
            'type' => 1, // 收入
            'money' => $amountYuan, // 元单位
            'status' => 0, // 冻结状态
            'remark' => $remark . ' (订单ID:' . $orderId . ')',
            'create_time' => time()
        ];

        $result = $stationMoneyModel->add($freezeData);
        if ($result) {
            \Think\Log::write('资金冻结成功 station_id=' . $stationId . ' amount=' . $amount . '分(' . $amountYuan . '元) order_id=' . $orderId, 'INFO');
        }

        return $result;
    }

    /**
     * 冻结服务站资金（新版本，使用正确的type）
     */
    private function freezeStationAmountV2($stationId, $amount, $orderId, $remark, $type = 5)
    {
        if ($amount <= 0) {
            \Think\Log::write('冻结金额为0，跳过冻结 station_id=' . $stationId . ' order_id=' . $orderId, 'INFO');
            return true;
        }

        try {
            // 开启事务
            $serviceStationModel = D('ServiceStation');
            $stationMoneyModel = D('StationMoney');

            $serviceStationModel->startTrans();

            // 将分单位转换为元单位（输入的amount是分单位）
            $amountYuan = round($amount / 100, 2);

            // 使用行级锁更新服务站冻结余额
            $stationInfo = $serviceStationModel->where(['id' => $stationId])->lock(true)->find();
            if (!$stationInfo) {
                throw new \Exception('服务站信息不存在');
            }

            // 增加冻结余额和总金额
            $freezeResult = $serviceStationModel->where(['id' => $stationId])
                ->save([
                    'freeze_price' => ['exp', 'freeze_price + ' . $amountYuan],
                    'total_price' => ['exp', 'total_price + ' . $amountYuan]
                ]);

            if ($freezeResult === false) {
                throw new \Exception('更新冻结余额失败');
            }

            // 记录资金明细（使用正确的type）
            $stationMoneyData = [
                'service_station_id' => $stationId,
                'type' => $type, // 5=培训奖励(冻结), 13=招就办佣金(冻结)
                'money' => $amountYuan, // 元单位
                'create_time' => time(),
                'remark' => '确认收款-' . ($type == 13 ? '招就办佣金' : '培训奖励') . ' ¥' . number_format($amountYuan, 2) . ' 已添加到冻结余额' . ($type == 13 ? '（不可提现）' : '') . ' [订单:' . $orderId . ']',
            ];

            $moneyResult = $stationMoneyModel->add($stationMoneyData);
            if (!$moneyResult) {
                throw new \Exception('记录资金明细失败');
            }

            // 提交事务
            $serviceStationModel->commit();

            \Think\Log::write('资金冻结成功 station_id=' . $stationId . ' amount=' . $amount . '分(' . $amountYuan . '元) type=' . $type . ' order_id=' . $orderId, 'INFO');
            return true;

        } catch (\Exception $e) {
            $serviceStationModel->rollback();
            \Think\Log::write('资金冻结失败: ' . $e->getMessage() . ', station_id=' . $stationId . ', order_id=' . $orderId, 'ERROR');
            return false;
        }
    }

    /**
     * 解冻并分发资金（新版本）
     */
    private function unlockReward($orderId, $reason)
    {
        try {
            // 开启事务
            $serviceStationModel = D('ServiceStation');
            $stationMoneyModel = D('StationMoney');

            $serviceStationModel->startTrans();

            // 更新奖励状态为已解锁
            $this->where(['id' => $orderId])->save(['reward_status' => 'unlocked']);

            // 获取订单信息
            $order = $this->where(['id' => $orderId])->find();
            if (!$order) {
                throw new \Exception('订单不存在');
            }

            // 查找相关的冻结资金记录
            $freezeRecords = $stationMoneyModel->where([
                'service_station_id' => $order['station_id'],
                'type' => 5, // 培训奖励(冻结)
                'remark' => ['like', '%订单:' . $orderId . '%']
            ])->select();

            if (empty($freezeRecords)) {
                // 【修复】如果没有冻结记录但订单有奖励金额，这是异常情况
                if ($order['reward_station_amt'] > 0 || $order['station_profit'] > 0) {
                    \Think\Log::write('严重错误：订单有奖励但无冻结记录，数据不一致 order_id=' . $orderId . ' reward_amt=' . $order['reward_station_amt'] . ' station_profit=' . $order['station_profit'], 'ERROR');
                    $serviceStationModel->rollback();
                    throw new \Exception('数据不一致：订单有奖励但无冻结记录，请联系技术人员处理');
                }

                \Think\Log::write('未找到需要解冻的资金记录，且订单无奖励金额 order_id=' . $orderId, 'INFO');
                $serviceStationModel->commit();
                return true;
            }

            $totalUnfreezeAmount = 0;
            foreach ($freezeRecords as $record) {
                $totalUnfreezeAmount += $record['money'];

                // 创建解冻记录
                $unfreezeData = [
                    'service_station_id' => $order['station_id'],
                    'type' => 6, // 培训奖励(解冻)
                    'money' => $record['money'], // 元单位
                    'create_time' => time(),
                    'remark' => '服务完成-培训奖励解冻 ¥' . number_format($record['money'], 2) . ' 已转入可用余额 [订单:' . $orderId . ']',
                ];

                $unfreezeResult = $stationMoneyModel->add($unfreezeData);
                if (!$unfreezeResult) {
                    throw new \Exception('创建解冻记录失败');
                }
            }

            if ($totalUnfreezeAmount > 0) {
                // 使用行级锁更新服务站余额：将冻结余额转移到可用余额
                $stationInfo = $serviceStationModel->where(['id' => $order['station_id']])->lock(true)->find();
                if (!$stationInfo) {
                    throw new \Exception('服务站信息不存在');
                }

                $updateResult = $serviceStationModel->where(['id' => $order['station_id']])
                    ->save([
                        'freeze_price' => ['exp', 'freeze_price - ' . $totalUnfreezeAmount],
                        'price' => ['exp', 'price + ' . $totalUnfreezeAmount]
                        // total_price 保持不变
                    ]);

                if ($updateResult === false) {
                    throw new \Exception('更新服务站余额失败');
                }
            }

            // 提交事务
            $serviceStationModel->commit();

            \Think\Log::write('资金解冻成功 order_id=' . $orderId . ' amount=' . $totalUnfreezeAmount . '元 reason=' . $reason, 'INFO');
            return true;

        } catch (\Exception $e) {
            $serviceStationModel->rollback();
            \Think\Log::write('资金解冻失败: ' . $e->getMessage() . ', order_id=' . $orderId, 'ERROR');
            return false;
        }
    }

    /**
     * 冻结招就办佣金（新版本）
     * @param int $zsbId 招就办ID
     * @param int $commission 佣金金额（分单位）
     * @param int $orderId 订单ID
     * @return bool
     */
    private function freezeZsbCommissionV2($zsbId, $commission, $orderId)
    {
        try {
            $serviceStationModel = D('ServiceStation');
            $stationMoneyModel = D('StationMoney');

            $serviceStationModel->startTrans();

            // 转换为元单位
            $commissionYuan = $commission / 100;

            // 增加招就办的冻结余额和总余额
            $updateResult = $serviceStationModel->where(['id' => $zsbId])
                ->save([
                    'freeze_price' => ['exp', 'freeze_price + ' . $commissionYuan],
                    'total_price' => ['exp', 'total_price + ' . $commissionYuan]
                ]);

            if ($updateResult === false) {
                throw new \Exception('更新招就办冻结余额失败');
            }

            // 创建招就办资金明细记录
            $moneyData = [
                'service_station_id' => $zsbId,
                'type' => 13, // 招就办佣金(冻结)
                'money' => $commissionYuan,
                'create_time' => time(),
                'remark' => '确认收款-招就办佣金 ¥' . number_format($commissionYuan, 2) . ' 已添加到冻结余额（不可提现） [订单:' . $orderId . ']',
            ];

            $moneyResult = $stationMoneyModel->add($moneyData);
            if (!$moneyResult) {
                throw new \Exception('创建招就办资金明细失败');
            }

            $serviceStationModel->commit();

            \Think\Log::write('招就办佣金冻结成功 zsb_id=' . $zsbId . ' commission=' . $commission . '分（' . $commissionYuan . '元）', 'INFO');
            return true;

        } catch (\Exception $e) {
            $serviceStationModel->rollback();
            \Think\Log::write('招就办佣金冻结失败: ' . $e->getMessage() . ', zsb_id=' . $zsbId . ', order_id=' . $orderId, 'ERROR');
            return false;
        }
    }

    /**
     * 招就办订单完成处理（新版本）
     */
    private function completeZsbOrderV2($orderId)
    {
        try {
            // 开启事务
            $serviceStationModel = D('ServiceStation');
            $stationMoneyModel = D('StationMoney');

            $serviceStationModel->startTrans();

            // 更新奖励状态
            $this->where(['id' => $orderId])->save(['reward_status' => 'unlocked']);

            // 获取订单信息
            $order = $this->where(['id' => $orderId])->find();
            if (!$order || $order['zsb_id'] <= 0) {
                throw new \Exception('订单不存在或不是招就办订单');
            }

            // 查找服务站的冻结资金记录
            $stationFreezeRecords = $stationMoneyModel->where([
                'service_station_id' => $order['station_id'],
                'type' => 5, // 培训奖励(冻结)
                'remark' => ['like', '%订单:' . $orderId . '%']
            ])->select();

            // 查找招就办的冻结资金记录
            $zsbFreezeRecords = $stationMoneyModel->where([
                'service_station_id' => $order['zsb_id'],
                'type' => 13, // 招就办佣金(冻结)
                'remark' => ['like', '%订单:' . $orderId . '%']
            ])->select();

            // 解冻服务站资金（总收益：服务站收益+招就办收益）
            $stationUnfreezeAmount = 0;
            foreach ($stationFreezeRecords as $record) {
                $stationUnfreezeAmount += $record['money'];

                // 创建服务站解冻记录
                $unfreezeData = [
                    'service_station_id' => $order['station_id'],
                    'type' => 6, // 培训奖励(解冻)
                    'money' => $record['money'],
                    'create_time' => time(),
                    'remark' => '服务完成-招就办培训总收益解冻 ¥' . number_format($record['money'], 2) . ' 已转入可用余额 [订单:' . $orderId . ']',
                ];

                $unfreezeResult = $stationMoneyModel->add($unfreezeData);
                if (!$unfreezeResult) {
                    throw new \Exception('创建服务站解冻记录失败');
                }
            }

            // 解冻招就办资金（从冻结状态转为不可提现的统计状态）
            $zsbUnfreezeAmount = 0;
            foreach ($zsbFreezeRecords as $record) {
                $zsbUnfreezeAmount += $record['money'];

                // 创建招就办解冻记录
                $unfreezeData = [
                    'service_station_id' => $order['zsb_id'],
                    'type' => 12, // 招就办奖励(解冻但不可提现)
                    'money' => $record['money'],
                    'create_time' => time(),
                    'remark' => '服务完成-招就办佣金解冻 ¥' . number_format($record['money'], 2) . ' 已转入统计余额（不可提现） [订单:' . $orderId . ']',
                ];

                $unfreezeResult = $stationMoneyModel->add($unfreezeData);
                if (!$unfreezeResult) {
                    throw new \Exception('创建招就办解冻记录失败');
                }
            }

            // 更新服务站余额（将冻结余额转移到可用余额）
            if ($stationUnfreezeAmount > 0) {
                $updateResult = $serviceStationModel->where(['id' => $order['station_id']])
                    ->save([
                        'freeze_price' => ['exp', 'freeze_price - ' . $stationUnfreezeAmount],
                        'price' => ['exp', 'price + ' . $stationUnfreezeAmount]
                    ]);

                if ($updateResult === false) {
                    throw new \Exception('更新服务站余额失败');
                }
            }

            // 更新招就办余额（将冻结余额转为不可提现的统计余额）
            if ($zsbUnfreezeAmount > 0) {
                $updateResult = $serviceStationModel->where(['id' => $order['zsb_id']])
                    ->save([
                        'freeze_price' => ['exp', 'freeze_price - ' . $zsbUnfreezeAmount]
                        // 注意：招就办收益解冻后保持在total_price中，但不转入price（不可提现）
                        // total_price保持不变，这样 total_price - freeze_price - price = 不可提现余额
                    ]);

                if ($updateResult === false) {
                    throw new \Exception('更新招就办余额失败');
                }
            }

            // 提交事务
            $serviceStationModel->commit();

            \Think\Log::write('招就办订单资金解冻成功 order_id=' . $orderId . ' station_amount=' . $stationUnfreezeAmount . '元 zsb_amount=' . $zsbUnfreezeAmount . '元', 'INFO');
            return true;

        } catch (\Exception $e) {
            $serviceStationModel->rollback();
            \Think\Log::write('招就办订单资金解冻失败: ' . $e->getMessage() . ', order_id=' . $orderId, 'ERROR');
            return false;
        }
    }

    /**
     * 处理取消退款
     */
    private function handleCancelRefund($orderId, $order)
    {
        // 检查订单状态是否需要资金回退
        // 需要检查取消前的状态，而不是取消后的状态
        $previousStatus = $this->getPreviousSubStatus($orderId);
        $needRefund = $this->needRefundByStatus($previousStatus);

        if (!$needRefund) {
            \Think\Log::write('订单取消前状态为 ' . $previousStatus . '，无需资金回退 order_id=' . $orderId, 'INFO');
            return;
        }

        // 根据转换前的订单状态确定操作类型描述
        $actionType = $this->getRefundActionTypeFromHistory($orderId, $order);
        \Think\Log::write('【修复版本】handleCancelRefund: actionType=' . $actionType . ' order_id=' . $orderId, 'INFO');

        // 检查是否为招就办订单
        if (!empty($order['zsb_id']) && $order['zsb_id'] > 0) {
            // 招就办订单：使用专门的回退逻辑
            $this->handleZjbCancelRefundV2($orderId, $order, $actionType);
        } else {
            // 普通订单：使用传统回退逻辑
            $this->handleNormalCancelRefundV2($orderId, $order, $actionType);
        }

        // 记录退款明细（这里可以根据实际业务需求添加退款处理逻辑）
        \Think\Log::write($actionType . '退款处理: 订单ID=' . $orderId . ', 退款金额=' . $order['fee_amount'], 'INFO');
    }

    /**
     * 根据订单状态判断是否需要资金回退
     * @param string $subStatus 订单子状态
     * @return bool
     */
    private function needRefundByStatus($subStatus)
    {
        // 只有在 full_paid 及之后的状态才会有资金冻结，才需要回退
        $statusesNeedRefund = [
            'full_paid',        // 已全额付款

            // 培训流程
            'pending_notice',   // 待通知培训
            'notice_sent',      // 已通知培训
            'docs_signed',      // 已签署文件
            'submitted_institute', // 已提交至培训机构
            'in_training',      // 参与培训中
            'end_training',     // 培训结束 【修复：添加缺失状态】

            // 入职流程 【修复：添加所有入职流程状态】
            'waiting_examination',  // 待通知笔试
            'note_examination',     // 已通知笔试
            'in_examinationloyed',  // 已参加笔试
            'waiting_interview',    // 待通知面试
            'note_interview',       // 已通知面试
            'int_interviewyed',     // 已参加面试
            'waiting_notice',       // 等待入职通知
            'employed',             // 已入职

            // 服务审查
            'confirming'        // 服务完成确认中
        ];

        return in_array($subStatus, $statusesNeedRefund);
    }

    /**
     * 获取订单的前一个状态（用于取消时判断是否需要退款）
     * @param int $orderId 订单ID
     * @return string 前一个子状态
     */
    private function getPreviousSubStatus($orderId)
    {
        // 从状态历史表中获取最后两条记录
        $historyModel = D('TrainingOrderStatusHistory');
        $histories = $historyModel->where(['order_id' => $orderId])
                                 ->order('create_time DESC')
                                 ->limit(2)
                                 ->select();

        if (count($histories) >= 2) {
            // 返回倒数第二条记录的to_sub_status（即取消前的状态）
            return $histories[1]['to_sub_status'];
        } elseif (count($histories) == 1) {
            // 只有一条记录，返回from_sub_status
            return $histories[0]['from_sub_status'];
        } else {
            // 没有历史记录，返回默认状态
            return 'initial_contact';
        }
    }

    /**
     * 根据状态历史确定退款操作类型描述
     */
    private function getRefundActionTypeFromHistory($orderId, $order)
    {
        // 查询最近的状态变更记录，获取转换前的状态
        $historyModel = D('TrainingOrderStatusHistory');
        $latestHistory = $historyModel->where(['order_id' => $orderId])
            ->order('create_time DESC')
            ->find();

        if ($latestHistory && $latestHistory['to_sub_status'] === 'terminated') {
            // 如果最新记录是转换到terminated状态，使用转换前的状态
            $fromStatus = $latestHistory['from_sub_status'];
        } else {
            // 否则使用当前状态
            $fromStatus = $order['sub_status'] ?? $order['order_status'];
        }

        // 根据转换前状态判断操作类型（按实际业务阶段分类）
        switch ($fromStatus) {
            // 服务完成阶段
            case 'confirming':
            case 'service_review':
                \Think\Log::write('getRefundActionTypeFromHistory: 返回服务终止 from_status=' . $fromStatus . ' order_id=' . $orderId, 'INFO');
                return '服务终止';

            // 入职阶段
            case 'employed':            // 已入职
                \Think\Log::write('getRefundActionTypeFromHistory: 返回入职终止 from_status=' . $fromStatus . ' order_id=' . $orderId, 'INFO');
                return '入职终止';

            // 培训相关阶段
            case 'pending_notice':      // 待通知培训
            case 'notice_sent':         // 已通知培训
            case 'docs_signed':         // 已签署文件
            case 'submitted_institute': // 已提交至培训机构
            case 'in_training':         // 参与培训中
            case 'training_completed':  // 培训完成
                \Think\Log::write('getRefundActionTypeFromHistory: 返回培训终止 from_status=' . $fromStatus . ' order_id=' . $orderId, 'INFO');
                return '培训终止';

            // 早期阶段（报名、签约、付款）
            default:
                \Think\Log::write('getRefundActionTypeFromHistory: 返回取消报名 from_status=' . $fromStatus . ' order_id=' . $orderId, 'INFO');
                return '取消报名';
        }
    }

    /**
     * 处理招就办订单取消的奖励回退
     * @param int $orderId 订单ID
     * @param array $order 订单信息
     * @param string $actionType 操作类型
     * @throws \Exception
     */
    private function handleZjbCancelRefundV2($orderId, $order, $actionType = '取消报名') {
        $serviceStationModel = D('ServiceStation');
        $stationMoneyModel = D('StationMoney');

        try {
            // 【修复】不再开启新事务，因为已经在updateOrderStatusV2的事务中
            // $serviceStationModel->startTrans();

            // 1. 处理服务站总收益回退（station_profit + zsb_commission）
            $stationTotalReward = $order['station_profit'] + $order['zsb_commission']; // 分单位
            if ($stationTotalReward > 0) {
                // 使用行级锁获取服务站信息
                $stationInfo = $serviceStationModel->where(['id' => $order['station_id']])->lock(true)->find();
                if (!$stationInfo) {
                    throw new \Exception('服务站信息不存在');
                }

                // 将分转换为元进行余额检查（z_service_station表存储的是元单位）
                $stationTotalRewardYuan = $stationTotalReward / 100;

                // 检查冻结余额是否充足
                if ($stationInfo['freeze_price'] < $stationTotalRewardYuan) {
                    throw new \Exception('服务站冻结余额不足，无法回退奖励');
                }

                // 扣除服务站冻结余额和总余额
                $transferResult = $serviceStationModel->where(['id' => $order['station_id']])
                    ->save([
                        'freeze_price' => ['exp', 'freeze_price - ' . $stationTotalRewardYuan],
                        'total_price' => ['exp', 'total_price - ' . $stationTotalRewardYuan]
                    ]);

                if ($transferResult === false) {
                    throw new \Exception('服务站奖励回退失败');
                }

                // 记录服务站资金明细 - 奖励回退（z_station_money表存储的是元单位）
                $stationMoneyData = [
                    'service_station_id' => $order['station_id'],
                    'type' => 11, // 取消报名奖励回退
                    'money' => $stationTotalReward / 100, // 存储绝对值，符号由type类型决定
                    'create_time' => time(),
                    'remark' => $actionType . '-招就办奖励回退 ¥' . number_format($stationTotalReward / 100, 2) . ' 从冻结余额和总余额中扣除（含招就办佣金） [订单:' . $orderId . ']',
                ];

                $stationMoneyResult = $stationMoneyModel->add($stationMoneyData);
                if (!$stationMoneyResult) {
                    throw new \Exception('记录服务站资金明细失败');
                }
            }

            // 2. 处理招就办收益回退
            if ($order['zsb_commission'] > 0) {
                // 使用行级锁获取招就办信息
                $zsbInfo = $serviceStationModel->where(['id' => $order['zsb_id']])->lock(true)->find();
                if (!$zsbInfo) {
                    throw new \Exception('招就办信息不存在');
                }

                // 将分转换为元进行余额检查（z_service_station表存储的是元单位）
                $zsbCommissionYuan = $order['zsb_commission'] / 100;

                // 检查冻结余额是否充足
                if ($zsbInfo['freeze_price'] < $zsbCommissionYuan) {
                    throw new \Exception('招就办冻结余额不足，无法回退奖励');
                }

                // 扣除招就办冻结余额和总余额
                $zsbTransferResult = $serviceStationModel->where(['id' => $order['zsb_id']])
                    ->save([
                        'freeze_price' => ['exp', 'freeze_price - ' . $zsbCommissionYuan],
                        'total_price' => ['exp', 'total_price - ' . $zsbCommissionYuan]
                    ]);

                if ($zsbTransferResult === false) {
                    throw new \Exception('招就办奖励回退失败');
                }

                // 记录招就办资金明细 - 奖励回退（z_station_money表存储的是元单位）
                $zsbMoneyData = [
                    'service_station_id' => $order['zsb_id'],
                    'type' => 11, // 取消报名奖励回退
                    'money' => $order['zsb_commission'] / 100, // 存储绝对值，符号由type类型决定
                    'create_time' => time(),
                    'remark' => $actionType . '-招就办奖励回退 ¥' . number_format($order['zsb_commission'] / 100, 2) . ' 从冻结余额和总余额中扣除 [订单:' . $orderId . ']',
                ];

                $zsbMoneyResult = $stationMoneyModel->add($zsbMoneyData);
                if (!$zsbMoneyResult) {
                    throw new \Exception('记录招就办资金明细失败');
                }
            }

            // 【修复】不再提交事务，由上层的updateOrderStatusV2统一提交
            // $serviceStationModel->commit();

            \Think\Log::write('招就办订单' . $actionType . '奖励回退成功 order_id=' . $orderId . ' station_total_reward=' . ($stationTotalReward / 100) . '元 zsb_commission=' . ($order['zsb_commission'] / 100) . '元', 'INFO');

        } catch (\Exception $e) {
            // 【修复】不再回滚事务，由上层的updateOrderStatusV2统一回滚
            // $serviceStationModel->rollback();
            \Think\Log::write('招就办订单' . $actionType . '奖励回退失败: ' . $e->getMessage() . ', 订单ID: ' . $orderId, 'ERROR');
            throw $e;
        }
    }

    /**
     * 处理普通订单取消的奖励回退
     * @param int $orderId 订单ID
     * @param array $order 订单信息
     * @param string $actionType 操作类型
     * @throws \Exception
     */
    private function handleNormalCancelRefundV2($orderId, $order, $actionType = '取消报名') {
        $serviceStationModel = D('ServiceStation');
        $stationMoneyModel = D('StationMoney');

        try {
            // 【修复】不再开启新事务，因为已经在updateOrderStatusV2的事务中
            // $serviceStationModel->startTrans();

            // 处理直属服务站奖励回退
            if ($order['reward_station_amt'] > 0) {
                // 使用行级锁获取服务站信息
                $stationInfo = $serviceStationModel->where(['id' => $order['station_id']])->lock(true)->find();
                if (!$stationInfo) {
                    throw new \Exception('服务站信息不存在');
                }

                // 将分转换为元进行余额检查（z_service_station表存储的是元单位）
                $rewardStationAmtYuan = $order['reward_station_amt'] / 100;

                // 检查冻结余额是否充足
                if ($stationInfo['freeze_price'] < $rewardStationAmtYuan) {
                    // 管理员强制取消：允许负余额，记录警告日志
                    \Think\Log::write('管理员强制取消订单，冻结余额不足但继续执行 station_id=' . $order['station_id'] . ' 需要=' . $rewardStationAmtYuan . '元 实际=' . $stationInfo['freeze_price'] . '元', 'WARN');

                    // 将冻结余额设为0，总余额相应减少
                    $actualDeduction = min($stationInfo['freeze_price'], $rewardStationAmtYuan);
                    $transferResult = $serviceStationModel->where(['id' => $order['station_id']])
                        ->save([
                            'freeze_price' => 0,
                            'total_price' => ['exp', 'total_price - ' . $actualDeduction]
                        ]);

                    if ($transferResult === false) {
                        throw new \Exception('服务站奖励回退失败');
                    }

                    // 记录资金变动
                    $stationMoneyModel->add([
                        'service_station_id' => $order['station_id'],
                        'type' => 11, // 订单取消奖励回退
                        'money' => $actualDeduction,
                        'create_time' => time(),
                        'remark' => '管理员强制' . $actionType . '-部分回退 ¥' . number_format($actualDeduction, 2) . ' [订单:' . $orderId . ']'
                    ]);

                    \Think\Log::write('普通订单强制取消奖励回退成功 order_id=' . $orderId . ' 实际回退=' . $actualDeduction . '元', 'WARN');
                } else {
                    // 正常回退流程
                    // 扣除冻结余额和总余额
                    $transferResult = $serviceStationModel->where(['id' => $order['station_id']])
                        ->save([
                            'freeze_price' => ['exp', 'freeze_price - ' . $rewardStationAmtYuan],
                            'total_price' => ['exp', 'total_price - ' . $rewardStationAmtYuan]
                        ]);

                    if ($transferResult === false) {
                        throw new \Exception('服务站奖励回退失败');
                    }

                    // 记录资金变动
                    $stationMoneyModel->add([
                        'service_station_id' => $order['station_id'],
                        'type' => 11, // 订单取消奖励回退
                        'money' => $rewardStationAmtYuan,
                        'create_time' => time(),
                        'remark' => $actionType . '-奖励回退 ¥' . number_format($rewardStationAmtYuan, 2) . ' [订单:' . $orderId . ']'
                    ]);

                    \Think\Log::write('普通订单取消奖励回退成功 order_id=' . $orderId . ' reward_station_amt=' . $rewardStationAmtYuan . '元', 'INFO');
                }
            }

            // 【修复】不再提交事务，由上层的updateOrderStatusV2统一提交
            // $serviceStationModel->commit();

            \Think\Log::write('普通订单取消奖励回退成功 order_id=' . $orderId . ' reward_station_amt=' . ($order['reward_station_amt'] / 100) . '元', 'INFO');

        } catch (\Exception $e) {
            // 【修复】不再回滚事务，由上层的updateOrderStatusV2统一回滚
            // $serviceStationModel->rollback();
            \Think\Log::write('普通订单取消奖励回退失败: ' . $e->getMessage() . ', 订单ID: ' . $orderId, 'ERROR');
            throw $e;
        }
    }

    /**
     * 创建招就办培训订单
     * @param array $data 订单数据
     * @return int|bool 成功返回订单ID，失败返回false
     */
    public function createZjbOrder($data)
    {
        try {
            $this->startTrans();

            // 验证必要字段
            if (empty($data['post_id']) || empty($data['station_id'])) {
                \Think\Log::write('创建招就办订单失败：缺少必要字段 post_id 或 station_id', 'ERROR');
                $this->rollback();
                return false;
            }

            // 验证招就办状态
            $zsbInfo = D('ServiceStation')->where([
                'id' => $data['station_id'],
                'zsb_type' => 2,
                'status' => 1
            ])->find();

            if (!$zsbInfo) {
                \Think\Log::write('创建招就办订单失败：招就办不存在或已禁用 zsb_id=' . $data['station_id'], 'ERROR');
                $this->rollback();
                return false;
            }

            // 验证关联服务站
            if (empty($zsbInfo['zsb_ref_station'])) {
                \Think\Log::write('创建招就办订单失败：招就办未关联服务站 zsb_id=' . $data['station_id'], 'ERROR');
                $this->rollback();
                return false;
            }

            $parentStation = D('ServiceStation')->where([
                'id' => $zsbInfo['zsb_ref_station'],
                'status' => 1
            ])->find();

            if (!$parentStation) {
                \Think\Log::write('创建招就办订单失败：关联服务站不存在 parent_station_id=' . $zsbInfo['zsb_ref_station'], 'ERROR');
                $this->rollback();
                return false;
            }

            // 获取招就办价格信息
            $priceInfo = $this->getZjbPriceInfo($data['post_id'], $data['station_id']);
            if (!$priceInfo) {
                \Think\Log::write('创建招就办订单失败：未找到价格配置 post_id=' . $data['post_id'] . ', zsb_id=' . $data['station_id'], 'ERROR');
                $this->rollback();
                return false;
            }

            // 验证价格合理性
            if ($priceInfo['zsb_price'] <= 0 || $priceInfo['zsb_cost_price'] < 0 || $priceInfo['platform_fee'] < 0) {
                \Think\Log::write('创建招就办订单失败：价格配置异常 ' . json_encode($priceInfo), 'ERROR');
                $this->rollback();
                return false;
            }

            // 设置招就办相关字段
            $data['zsb_id'] = $data['station_id']; // 招就办ID
            $data['station_id'] = $zsbInfo['zsb_ref_station']; // 实际服务站ID
            $data['zsb_price'] = $priceInfo['zsb_price']; // 招就办对外价格（分）
            $data['zsb_cost_price'] = $priceInfo['zsb_cost_price']; // 服务站成本价（分）
            $data['platform_fee'] = $priceInfo['platform_fee']; // 平台服务费（分）
            $data['station_profit'] = $priceInfo['station_profit']; // 服务站收益（分，可提现）
            $data['zsb_commission'] = $priceInfo['zsb_commission']; // 招就办收益（分，不可提现）

            // 设置报名费为招就办对外价格（保持分单位，数据库字段注释为分单位）
            $data['fee_amount'] = $priceInfo['zsb_price'];

            \Think\Log::write('创建招就办订单 - 价格信息: zsb_price=' . $data['zsb_price'] .
                ', zsb_cost_price=' . $data['zsb_cost_price'] .
                ', platform_fee=' . $data['platform_fee'] .
                ', station_profit=' . $data['station_profit'] .
                ', zsb_commission=' . $data['zsb_commission'], 'INFO');

            // 复用现有的createOrder方法（注意：平台收入计算会在createOrder中进行）
            $orderId = $this->createOrder($data);

            if (!$orderId) {
                \Think\Log::write('创建招就办订单失败：createOrder返回false', 'ERROR');
                $this->rollback();
                return false;
            }

            $this->commit();
            \Think\Log::write('创建招就办订单成功 order_id=' . $orderId, 'INFO');
            return $orderId;

        } catch (\Exception $e) {
            $this->rollback();
            \Think\Log::write('创建招就办订单异常：' . $e->getMessage(), 'ERROR');
            return false;
        }
    }

    /**
     * 获取招就办价格信息
     * @param int $postId 岗位ID
     * @param int $zsbId 招就办ID
     * @return array|false 价格信息数组或false
     */
    public function getZjbPriceInfo($postId, $zsbId)
    {
        // 从ZsbPostPriceModel获取价格配置
        $zsbPriceModel = D('ZsbPostPrice');
        $priceConfig = $zsbPriceModel->where([
            'post_id' => $postId,
            'zsb_id' => $zsbId,
            'status' => 1
        ])->find();

        if (!$priceConfig) {
            return false;
        }

        // 获取岗位基本信息
        $postInfo = D('ProjectPost')->where(['id' => $postId, 'status' => 1])->find();
        if (!$postInfo) {
            return false;
        }

        // 计算平台服务费（基于招就办对外价格）
        $platformFee = $zsbPriceModel->calculatePlatformFee(
            $priceConfig['sale_price'], // 招就办对外价格（分）
            $postInfo['service_price'] // 岗位服务价格（元）
        );

        // 获取基准成本价（服务站身份的成本价）
        $baseCost = $zsbPriceModel->getBaseCost($postId); // 元单位
        $baseCostCents = $baseCost * 100; // 转换为分单位

        // 计算服务站收益：服务站成本价 - 基准成本价
        $stationProfit = $priceConfig['cost_price'] - $baseCostCents;

        // 确保收益不为负数
        if ($stationProfit < 0) {
            \Think\Log::write('警告：服务站收益为负数 post_id=' . $postId . ', zsb_id=' . $zsbId .
                ', cost_price=' . $priceConfig['cost_price'] . ', base_cost=' . $baseCostCents .
                ', profit=' . $stationProfit, 'WARN');
            $stationProfit = 0;
        }

        return [
            'zsb_price' => $priceConfig['sale_price'], // 招就办对外价格（分）
            'zsb_cost_price' => $priceConfig['cost_price'], // 服务站成本价（分）
            'platform_fee' => $platformFee, // 平台服务费（分）
            'station_profit' => $stationProfit, // 服务站收益（分，可提现）
            'zsb_commission' => $priceConfig['commission'], // 招就办收益（分，不可提现）
        ];
    }

    /**
     * 重新计算招就办订单的总收益（服务站收益+招就办收益）
     * @param array $order 订单信息
     * @return float 总收益（元单位）
     * @deprecated 已废弃，改为直接使用数据库存储的收益字段
     */
    public function calculateZjbTotalProfit($order)
    {
        // 注释：此方法已废弃，改为直接使用数据库存储的收益字段
        // 直接使用数据库中的 station_profit 和 zsb_commission 字段
        if (empty($order['zsb_id']) || $order['zsb_id'] <= 0) {
            return 0;
        }

        // 直接使用存储的收益字段（分单位转元单位）
        $stationProfit = !empty($order['station_profit']) ? $order['station_profit'] : 0;
        $zsbCommission = !empty($order['zsb_commission']) ? $order['zsb_commission'] : 0;

        return round(($stationProfit + $zsbCommission) / 100, 2);
    }

    /**
     * 招就办订单收益分配
     * @param array $order 订单信息
     * @return bool 成功返回true，失败返回false
     */
    public function distributeZjbProfit($order)
    {
        if (empty($order['zsb_id']) || $order['zsb_id'] <= 0) {
            \Think\Log::write('收益分配失败：非招就办订单 order_id=' . $order['id'], 'ERROR');
            return false;
        }

        // 验证订单状态
        if ($order['order_status'] !== 'completed') {
            \Think\Log::write('收益分配失败：订单状态不正确 order_id=' . $order['id'] . ', status=' . $order['order_status'], 'ERROR');
            return false;
        }

        // 防止重复分配
        if ($this->checkProfitDistributed($order['id'])) {
            \Think\Log::write('收益分配失败：订单已分配过收益 order_id=' . $order['id'], 'WARN');
            return false;
        }

        try {
            $this->startTrans();

            // 获取服务站信息（招就办所属的服务站）
            $stationInfo = D('ServiceStation')->where([
                'id' => $order['zsb_id'],
                'zsb_type' => 2,
                'status' => 1
            ])->find();

            if (!$stationInfo) {
                \Think\Log::write('收益分配失败：招就办信息不存在或类型错误 zsb_id=' . $order['zsb_id'], 'ERROR');
                $this->rollback();
                return false;
            }

            // 获取上级服务站信息
            $parentStationId = $stationInfo['zsb_ref_station'];
            if (!$parentStationId) {
                \Think\Log::write('收益分配失败：招就办没有关联服务站 zsb_id=' . $order['zsb_id'], 'ERROR');
                $this->rollback();
                return false;
            }

            $parentStation = D('ServiceStation')->where([
                'id' => $parentStationId,
                'status' => 1
            ])->find();

            if (!$parentStation) {
                \Think\Log::write('收益分配失败：上级服务站不存在 parent_station_id=' . $parentStationId, 'ERROR');
                $this->rollback();
                return false;
            }

            // 验证收益金额合理性
            if ($order['station_profit'] < 0 || $order['zsb_commission'] < 0) {
                \Think\Log::write('收益分配失败：收益金额异常 station_profit=' . $order['station_profit'] . ', zsb_commission=' . $order['zsb_commission'], 'ERROR');
                $this->rollback();
                return false;
            }

            // 服务站收益分配（可提现）
            if ($order['station_profit'] > 0) {
                $stationBalanceResult = D('ServiceStation')->addBalance(
                    $parentStationId,
                    $order['station_profit'],
                    true, // 可提现
                    '招就办订单收益分配 - 订单ID:' . $order['id']
                );

                if (!$stationBalanceResult) {
                    \Think\Log::write('服务站收益分配失败 station_id=' . $parentStationId . ', amount=' . $order['station_profit'], 'ERROR');
                    $this->rollback();
                    return false;
                }

                \Think\Log::write('服务站收益分配成功 station_id=' . $parentStationId . ', amount=' . $order['station_profit'] . '分（可提现）', 'INFO');
            }

            // 招就办收益分配（不可提现，仅统计）
            if ($order['zsb_commission'] > 0) {
                $zsbBalanceResult = D('ServiceStation')->addBalance(
                    $order['zsb_id'],
                    $order['zsb_commission'],
                    false, // 不可提现
                    '招就办订单收益分配 - 订单ID:' . $order['id']
                );

                if (!$zsbBalanceResult) {
                    \Think\Log::write('招就办收益分配失败 zsb_id=' . $order['zsb_id'] . ', amount=' . $order['zsb_commission'], 'ERROR');
                    $this->rollback();
                    return false;
                }

                \Think\Log::write('招就办收益分配成功 zsb_id=' . $order['zsb_id'] . ', amount=' . $order['zsb_commission'] . '分（不可提现）', 'INFO');
            }

            // 标记收益已分配
            $this->markProfitDistributed($order['id']);

            $this->commit();
            return true;

        } catch (\Exception $e) {
            $this->rollback();
            \Think\Log::write('招就办收益分配异常：' . $e->getMessage(), 'ERROR');
            return false;
        }
    }

    /**
     * 检查订单是否已分配收益
     * @param int $orderId 订单ID
     * @return bool
     */
    private function checkProfitDistributed($orderId)
    {
        // 检查是否有收益分配记录
        $record = D('StationMoney')->where([
            'remark' => ['like', '%订单ID:' . $orderId . '%'],
            'type' => 2, // 收入类型
            'status' => 4 // 成功状态
        ])->find();

        return !empty($record);
    }

    /**
     * 标记订单收益已分配
     * @param int $orderId 订单ID
     * @return bool
     */
    private function markProfitDistributed($orderId)
    {
        // 更新订单的奖励状态为已发放
        return $this->where(['id' => $orderId])->save([
            'reward_status' => 'distributed',
            'update_time' => time()
        ]);
    }

    /**
     * 完成招就办订单并分配收益
     * @param int $orderId 订单ID
     * @return bool 成功返回true，失败返回false
     */
    public function completeZjbOrder($orderId)
    {
        $order = $this->where(['id' => $orderId])->find();
        if (!$order) {
            \Think\Log::write('完成招就办订单失败：订单不存在 order_id=' . $orderId, 'ERROR');
            return false;
        }

        // 检查是否为招就办订单
        if (empty($order['zsb_id']) || $order['zsb_id'] <= 0) {
            \Think\Log::write('完成招就办订单失败：非招就办订单 order_id=' . $orderId, 'ERROR');
            return false;
        }

        // 更新订单状态为已完成
        $updateResult = $this->updateOrderStatus($orderId, 'completed');
        if (!$updateResult) {
            \Think\Log::write('完成招就办订单失败：状态更新失败 order_id=' . $orderId, 'ERROR');
            return false;
        }

        // 分配收益
        $profitResult = $this->distributeZjbProfit($order);
        if (!$profitResult) {
            \Think\Log::write('完成招就办订单失败：收益分配失败 order_id=' . $orderId, 'ERROR');
            return false;
        }

        \Think\Log::write('招就办订单完成成功 order_id=' . $orderId, 'INFO');
        return true;
    }
}
